<template>
  <div class="mobile-navigation">
    <!-- Bottom Navigation Bar -->
    <div class="btm-nav btm-nav-lg bg-base-100 border-t border-base-300 safe-area-bottom">
      <router-link
        v-for="item in navigationItems"
        :key="item.name"
        :to="item.to"
        class="nav-item"
        :class="{ 'active': isActiveRoute(item.to) }"
        @click="trackNavigation(item.name)"
      >
        <Icon :name="item.icon" :size="item.size || 'md'" />
        <span class="btm-nav-label text-xs">{{ $t(item.label) }}</span>
        
        <!-- Badge for notifications -->
        <div
          v-if="item.badge && getBadgeCount(item.badge) > 0"
          class="badge badge-primary badge-sm absolute -top-1 -right-1"
        >
          {{ getBadgeCount(item.badge) }}
        </div>
      </router-link>
    </div>

    <!-- Floating Action Button -->
    <div
      v-if="showFAB"
      class="fab-container fixed bottom-20 right-4 z-50"
    >
      <div class="dropdown dropdown-top dropdown-end">
        <div
          tabindex="0"
          role="button"
          class="btn btn-circle btn-primary btn-lg shadow-lg fab-button"
          :class="{ 'fab-open': fabOpen }"
          @click="toggleFAB"
        >
          <Icon :name="fabOpen ? 'close' : 'plus'" size="lg" class="transition-transform duration-300" />
        </div>
        
        <ul
          v-if="fabOpen"
          tabindex="0"
          class="dropdown-content menu bg-base-100 rounded-box z-[1] w-52 p-2 shadow-xl border border-base-300 mb-2"
        >
          <li v-for="action in fabActions" :key="action.name">
            <a
              @click="handleFABAction(action)"
              class="flex items-center gap-3 p-3 hover:bg-base-200 rounded-lg"
            >
              <Icon :name="action.icon" size="md" :class="action.iconClass" />
              <div>
                <div class="font-medium">{{ $t(action.label) }}</div>
                <div class="text-xs text-base-content/70">{{ $t(action.description) }}</div>
              </div>
            </a>
          </li>
        </ul>
      </div>
    </div>

    <!-- Quick Actions Drawer -->
    <div class="drawer drawer-end">
      <input id="quick-actions-drawer" type="checkbox" class="drawer-toggle" v-model="quickActionsOpen" />
      <div class="drawer-side z-50">
        <label for="quick-actions-drawer" aria-label="close sidebar" class="drawer-overlay"></label>
        <div class="menu bg-base-100 text-base-content min-h-full w-80 p-4">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold">{{ $t('mobile.quickActions', 'Quick Actions') }}</h3>
            <button
              class="btn btn-ghost btn-sm btn-circle"
              @click="quickActionsOpen = false"
            >
              <Icon name="close" size="sm" />
            </button>
          </div>
          
          <div class="space-y-2">
            <div
              v-for="action in quickActions"
              :key="action.name"
              class="card bg-base-200 hover:bg-base-300 cursor-pointer transition-colors"
              @click="handleQuickAction(action)"
            >
              <div class="card-body p-4">
                <div class="flex items-center gap-3">
                  <div class="avatar">
                    <div class="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                      <Icon :name="action.icon" size="md" class="text-primary" />
                    </div>
                  </div>
                  <div class="flex-1">
                    <h4 class="font-medium">{{ $t(action.label) }}</h4>
                    <p class="text-sm text-base-content/70">{{ $t(action.description) }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useAnalytics } from '@/composables/useAnalytics'
import { useNotifications } from '@/composables/useNotifications'
import Icon from '@/components/common/Icon.vue'

interface NavigationItem {
  name: string
  label: string
  to: string
  icon: string
  size?: string
  badge?: string
  requiresAuth?: boolean
}

interface FABAction {
  name: string
  label: string
  description: string
  icon: string
  iconClass?: string
  action: string
  requiresAuth?: boolean
}

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()
const { trackButtonClick } = useAnalytics()
const { showSuccess } = useNotifications()

const fabOpen = ref(false)
const quickActionsOpen = ref(false)

// Navigation items for bottom nav
const navigationItems = computed<NavigationItem[]>(() => [
  {
    name: 'home',
    label: 'nav.home',
    to: '/',
    icon: 'home'
  },
  // {
  //   name: 'services',
  //   label: 'nav.services',
  //   to: '/services',
  //   icon: 'grid'
  // },
  {
    name: 'contact',
    label: 'nav.contact',
    to: '/contact',
    icon: 'message',
    badge: 'messages'
  },
  {
    name: 'dashboard',
    label: 'nav.dashboard',
    to: '/dashboard',
    icon: 'chart',
    requiresAuth: true
  },
  {
    name: 'profile',
    label: 'nav.profile',
    to: '/profile',
    icon: 'user',
    requiresAuth: true
  }
].filter(item => !item.requiresAuth || authStore.isAuthenticated))

// FAB actions
const fabActions = computed<FABAction[]>(() => [
  {
    name: 'contact',
    label: 'mobile.fab.contact',
    description: 'mobile.fab.contactDesc',
    icon: 'message',
    iconClass: 'text-primary',
    action: 'contact'
  },
  {
    name: 'call',
    label: 'mobile.fab.call',
    description: 'mobile.fab.callDesc',
    icon: 'phone',
    iconClass: 'text-success',
    action: 'call'
  },
  {
    name: 'whatsapp',
    label: 'mobile.fab.whatsapp',
    description: 'mobile.fab.whatsappDesc',
    icon: 'whatsapp',
    iconClass: 'text-green-500',
    action: 'whatsapp'
  },
  {
    name: 'quote',
    label: 'mobile.fab.quote',
    description: 'mobile.fab.quoteDesc',
    icon: 'calculator',
    iconClass: 'text-warning',
    action: 'quote'
  }
])

// Quick actions
const quickActions = computed(() => [
  {
    name: 'energy_audit',
    label: 'mobile.quick.energyAudit',
    description: 'mobile.quick.energyAuditDesc',
    icon: 'search',
    action: 'energy_audit'
  },
  {
    name: 'consultation',
    label: 'mobile.quick.consultation',
    description: 'mobile.quick.consultationDesc',
    icon: 'calendar',
    action: 'consultation'
  },
  {
    name: 'calculator',
    label: 'mobile.quick.calculator',
    description: 'mobile.quick.calculatorDesc',
    icon: 'calculator',
    action: 'calculator'
  },
  {
    name: 'support',
    label: 'mobile.quick.support',
    description: 'mobile.quick.supportDesc',
    icon: 'help',
    action: 'support'
  }
])

// Show FAB based on current route
const showFAB = computed(() => {
  const hideFABRoutes = ['/contact', '/services']
  return !hideFABRoutes.includes(route.path)
})

// Check if route is active
const isActiveRoute = (to: string): boolean => {
  if (to === '/') {
    return route.path === '/'
  }
  return route.path.startsWith(to)
}

// Get badge count for notifications
const getBadgeCount = (badgeType: string): number => {
  switch (badgeType) {
    case 'messages':
      return 0 // Implement message count logic
    default:
      return 0
  }
}

// Track navigation
const trackNavigation = (itemName: string) => {
  trackButtonClick(`mobile_nav_${itemName}`, 'bottom_navigation')
}

// Toggle FAB
const toggleFAB = () => {
  fabOpen.value = !fabOpen.value
  trackButtonClick('mobile_fab_toggle', 'floating_action_button')
}

// Handle FAB actions
const handleFABAction = (action: FABAction) => {
  fabOpen.value = false
  trackButtonClick(`mobile_fab_${action.name}`, 'floating_action_button')

  switch (action.action) {
    case 'contact':
      router.push('/contact')
      break
    case 'call':
      window.open('tel:+1234567890', '_self')
      break
    case 'whatsapp':
      window.open('https://wa.me/1234567890?text=Hello%20HLenergy', '_blank')
      break
    case 'quote':
      router.push('/services?action=quote')
      break
  }
}

// Handle quick actions
const handleQuickAction = (action: any) => {
  quickActionsOpen.value = false
  trackButtonClick(`mobile_quick_${action.name}`, 'quick_actions')

  switch (action.action) {
    case 'energy_audit':
      router.push('/services/energy-audit')
      break
    case 'consultation':
      router.push('/services/consultation')
      break
    case 'calculator':
      router.push('/tools/calculator')
      break
    case 'support':
      router.push('/support')
      break
  }
}

// Close FAB when clicking outside
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.fab-container')) {
    fabOpen.value = false
  }
}

// Handle swipe gestures
const handleSwipe = (direction: string) => {
  if (direction === 'up' && !quickActionsOpen.value) {
    quickActionsOpen.value = true
  } else if (direction === 'down' && quickActionsOpen.value) {
    quickActionsOpen.value = false
  }
}

// Setup touch events for swipe detection
let touchStartY = 0
let touchEndY = 0

const handleTouchStart = (event: TouchEvent) => {
  touchStartY = event.changedTouches[0].screenY
}

const handleTouchEnd = (event: TouchEvent) => {
  touchEndY = event.changedTouches[0].screenY
  const swipeThreshold = 50
  const swipeDistance = touchStartY - touchEndY

  if (Math.abs(swipeDistance) > swipeThreshold) {
    if (swipeDistance > 0) {
      handleSwipe('up')
    } else {
      handleSwipe('down')
    }
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  document.addEventListener('touchstart', handleTouchStart)
  document.addEventListener('touchend', handleTouchEnd)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  document.removeEventListener('touchstart', handleTouchStart)
  document.removeEventListener('touchend', handleTouchEnd)
})
</script>

<style scoped>
.mobile-navigation {
  position: relative;
}

.nav-item {
  position: relative;
  transition: all 0.2s ease;
}

.nav-item.active {
  color: hsl(var(--p));
}

.fab-container {
  transition: all 0.3s ease;
}

.fab-button {
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.fab-button:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.2);
}

.fab-open {
  transform: rotate(45deg);
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

/* Smooth transitions for drawer */
.drawer-side {
  transition: transform 0.3s ease;
}

/* Custom scrollbar for drawer */
.drawer-side .menu {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--bc) / 0.2) transparent;
}

.drawer-side .menu::-webkit-scrollbar {
  width: 4px;
}

.drawer-side .menu::-webkit-scrollbar-track {
  background: transparent;
}

.drawer-side .menu::-webkit-scrollbar-thumb {
  background: hsl(var(--bc) / 0.2);
  border-radius: 2px;
}

/* Haptic feedback simulation */
.nav-item:active,
.fab-button:active,
.card:active {
  transform: scale(0.95);
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .fab-button {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  }
  
  .fab-button:hover {
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.4);
  }
}

/* Hide on desktop */
@media (min-width: 768px) {
  .mobile-navigation {
    display: none;
  }
}
</style>
