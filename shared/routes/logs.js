const express = require('express');
const router = express.Router();

/**
 * @swagger
 * /api/v1/logs/system:
 *   get:
 *     summary: Get system logs
 *     tags: [Logs]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *         description: Number of logs to return
 *       - in: query
 *         name: level
 *         schema:
 *           type: string
 *           enum: [error, warn, info, debug]
 *         description: Filter by log level
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *           enum: [auth, api, database, system, socket]
 *         description: Filter by log category
 *       - in: query
 *         name: source
 *         schema:
 *           type: string
 *         description: Filter by log source
 *     responses:
 *       200:
 *         description: System logs retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     logs:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                           level:
 *                             type: string
 *                           message:
 *                             type: string
 *                           timestamp:
 *                             type: string
 *                           source:
 *                             type: string
 *                           category:
 *                             type: string
 *                           details:
 *                             type: string
 *                     count:
 *                       type: integer
 *                     timestamp:
 *                       type: string
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/system', async (req, res) => {
  try {
    const { Log } = require('../models');
    const limit = parseInt(req.query.limit) || 50;
    const level = req.query.level;
    const category = req.query.category;
    const source = req.query.source;

    if (!Log) {
      return res.status(503).json({
        success: false,
        message: 'Database not available'
      });
    }

    const whereClause = {};

    // Filter by level
    if (level) {
      whereClause.level = level;
    }

    // Filter by source
    if (source) {
      whereClause.source = source;
    }

    // Map category to source patterns
    if (category && !source) {
      switch (category) {
        case 'socket':
          whereClause.source = {
            [require('sequelize').Op.in]: ['socket_server', 'socket_analytics', 'socket_communication']
          };
          break;
        case 'auth':
          whereClause.source = {
            [require('sequelize').Op.in]: ['auth_service', 'authentication', 'login']
          };
          break;
        case 'api':
          whereClause.source = {
            [require('sequelize').Op.in]: ['api_server', 'express', 'routes']
          };
          break;
        case 'database':
          whereClause.source = {
            [require('sequelize').Op.in]: ['database', 'sequelize', 'mysql']
          };
          break;
        case 'system':
          whereClause.source = {
            [require('sequelize').Op.in]: ['system', 'server', 'application']
          };
          break;
      }
    }

    const logs = await Log.findAll({
      where: whereClause,
      order: [['createdAt', 'DESC']],
      limit: Math.min(limit, 100), // Cap at 100 logs
      attributes: ['id', 'level', 'message', 'meta', 'source', 'createdAt']
    });

    // Transform logs for frontend consumption
    const transformedLogs = logs.map(log => ({
      id: log.id,
      level: log.level,
      message: log.message,
      timestamp: log.createdAt,
      source: log.source,
      category: getCategoryFromSource(log.source),
      details: typeof log.meta === 'string' ? log.meta : JSON.stringify(log.meta, null, 2),
      stackTrace: log.meta?.stackTrace || log.meta?.error?.stack || null
    }));

    res.json({
      success: true,
      data: {
        logs: transformedLogs,
        count: transformedLogs.length,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Error fetching system logs:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch system logs',
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/v1/logs/socket:
 *   get:
 *     summary: Get Socket.io specific logs
 *     tags: [Logs]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *         description: Number of logs to return
 *       - in: query
 *         name: level
 *         schema:
 *           type: string
 *           enum: [error, warn, info, debug]
 *         description: Filter by log level
 *     responses:
 *       200:
 *         description: Socket logs retrieved successfully
 */
router.get('/socket', async (req, res) => {
  try {
    const { Log } = require('../models');
    const limit = parseInt(req.query.limit) || 50;
    const level = req.query.level;

    if (!Log) {
      return res.status(503).json({
        success: false,
        message: 'Database not available'
      });
    }

    const whereClause = {
      source: {
        [require('sequelize').Op.in]: ['socket_server', 'socket_analytics', 'socket_communication']
      }
    };

    if (level) {
      whereClause.level = level;
    }

    const logs = await Log.findAll({
      where: whereClause,
      order: [['createdAt', 'DESC']],
      limit: Math.min(limit, 100),
      attributes: ['id', 'level', 'message', 'meta', 'source', 'createdAt']
    });

    // Transform logs for frontend consumption
    const transformedLogs = logs.map(log => ({
      id: log.id,
      level: log.level,
      message: log.message,
      timestamp: log.createdAt,
      source: log.source,
      event: log.meta?.event?.type || log.meta?.type || 'system',
      userId: log.meta?.event?.userId || log.meta?.userId || null,
      socketId: log.meta?.event?.sessionId || log.meta?.socketId || null,
      ip: log.meta?.event?.ip || log.meta?.ip || null,
      details: log.meta
    }));

    res.json({
      success: true,
      data: {
        logs: transformedLogs,
        count: transformedLogs.length,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Error fetching socket logs:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch logs',
      error: error.message
    });
  }
});

// Helper function to determine category from source
function getCategoryFromSource(source) {
  if (!source) return 'system';
  
  if (source.includes('socket')) return 'socket';
  if (source.includes('auth') || source.includes('login')) return 'auth';
  if (source.includes('api') || source.includes('express') || source.includes('routes')) return 'api';
  if (source.includes('database') || source.includes('sequelize') || source.includes('mysql')) return 'database';
  
  return 'system';
}

module.exports = router;
