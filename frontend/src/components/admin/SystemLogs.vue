<template>
  <div class="modal modal-open">
    <div class="modal-box max-w-7xl">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-2xl font-bold text-primary">
          <Icon name="document" size="md" class="mr-2" />
          System Logs
        </h3>
        <button @click="$emit('close')" class="btn btn-ghost btn-circle">
          <Icon name="x" size="md" />
        </button>
      </div>

      <!-- Log Filters -->
      <div class="card bg-base-100 border border-base-200 mb-6">
        <div class="card-body">
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="form-control">
              <label class="label">Log Level</label>
              <select v-model="filters.level" class="select select-bordered select-sm">
                <option value="">All Levels</option>
                <option value="error">Error</option>
                <option value="warn">Warning</option>
                <option value="info">Info</option>
                <option value="debug">Debug</option>
              </select>
            </div>
            <div class="form-control">
              <label class="label">Category</label>
              <select v-model="filters.category" class="select select-bordered select-sm">
                <option value="">All Categories</option>
                <option value="auth">Authentication</option>
                <option value="api">API</option>
                <option value="database">Database</option>
                <option value="system">System</option>
              </select>
            </div>
            <div class="form-control">
              <label class="label">Date Range</label>
              <select v-model="filters.dateRange" class="select select-bordered select-sm">
                <option value="today">Today</option>
                <option value="week">Last 7 days</option>
                <option value="month">Last 30 days</option>
                <option value="all">All time</option>
              </select>
            </div>
            <div class="form-control">
              <label class="label">Search</label>
              <input 
                v-model="filters.search" 
                type="text" 
                placeholder="Search logs..." 
                class="input input-bordered input-sm"
              />
            </div>
          </div>
          <div class="flex gap-2 mt-4">
            <button @click="applyFilters" class="btn btn-primary btn-sm">
              <Icon name="filter" size="sm" class="mr-2" />
              Apply Filters
            </button>
            <button @click="clearFilters" class="btn btn-ghost btn-sm">
              <Icon name="x" size="sm" class="mr-2" />
              Clear
            </button>
            <button @click="refreshLogs" class="btn btn-accent btn-sm">
              <Icon name="refresh" size="sm" class="mr-2" />
              Refresh
            </button>
            <button @click="exportLogs" class="btn btn-secondary btn-sm">
              <Icon name="download" size="sm" class="mr-2" />
              Export
            </button>
          </div>
        </div>
      </div>

      <!-- Log Statistics -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div class="stat bg-error text-error-content rounded-lg">
          <div class="stat-title text-error-content/80">Errors</div>
          <div class="stat-value text-2xl">{{ logStats.errors }}</div>
        </div>
        <div class="stat bg-warning text-warning-content rounded-lg">
          <div class="stat-title text-warning-content/80">Warnings</div>
          <div class="stat-value text-2xl">{{ logStats.warnings }}</div>
        </div>
        <div class="stat bg-info text-info-content rounded-lg">
          <div class="stat-title text-info-content/80">Info</div>
          <div class="stat-value text-2xl">{{ logStats.info }}</div>
        </div>
        <div class="stat bg-success text-success-content rounded-lg">
          <div class="stat-title text-success-content/80">Debug</div>
          <div class="stat-value text-2xl">{{ logStats.debug }}</div>
        </div>
      </div>

      <!-- Log Entries -->
      <div class="card bg-base-100 border border-base-200">
        <div class="card-body">
          <div class="flex justify-between items-center mb-4">
            <h4 class="card-title text-lg">Log Entries</h4>
            <div class="flex gap-2">
              <button @click="toggleAutoRefresh" class="btn btn-ghost btn-sm">
                <Icon :name="autoRefresh ? 'pause' : 'play'" size="sm" class="mr-2" />
                {{ autoRefresh ? 'Pause' : 'Auto Refresh' }}
              </button>
              <button @click="clearAllLogs" class="btn btn-error btn-sm">
                <Icon name="trash" size="sm" class="mr-2" />
                Clear All
              </button>
            </div>
          </div>

          <!-- Loading State -->
          <div v-if="isLoading" class="flex items-center justify-center py-8">
            <div class="loading loading-spinner loading-lg text-primary"></div>
            <span class="ml-3 text-base-content/70">Loading system logs...</span>
          </div>

          <!-- Error State -->
          <div v-else-if="error" class="alert alert-error">
            <Icon name="warning" size="md" />
            <div>
              <h4 class="font-bold">Failed to load logs</h4>
              <div class="text-sm">{{ error }}</div>
            </div>
            <button @click="fetchLogs" class="btn btn-sm btn-outline">
              <Icon name="refresh" size="sm" />
              Retry
            </button>
          </div>

          <!-- Empty State -->
          <div v-else-if="filteredLogs.length === 0" class="text-center py-8">
            <Icon name="document-text" size="xl" class="text-base-content/30 mb-4" />
            <h3 class="text-lg font-semibold text-base-content/70 mb-2">No logs found</h3>
            <p class="text-base-content/50">Try adjusting your filters or check back later.</p>
          </div>

          <!-- Log Table -->
          <div v-else class="overflow-x-auto max-h-96">
            <table class="table table-zebra table-pin-rows w-full">
              <thead>
                <tr>
                  <th class="w-32">Time</th>
                  <th class="w-20">Level</th>
                  <th class="w-24">Category</th>
                  <th>Message</th>
                  <th class="w-24">Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="log in filteredLogs" :key="log.id" :class="getLogRowClass(log.level)">
                  <td class="font-mono text-xs">{{ formatTime(log.timestamp) }}</td>
                  <td>
                    <div class="badge badge-sm" :class="getLevelBadgeClass(log.level)">
                      {{ log.level.toUpperCase() }}
                    </div>
                  </td>
                  <td>
                    <div class="badge badge-outline badge-sm">
                      {{ log.category }}
                    </div>
                  </td>
                  <td class="font-mono text-sm">
                    <div class="max-w-xs truncate" :title="log.message">
                      {{ log.message.length > 50 ? log.message.substring(0, 50) + '...' : log.message }}
                    </div>
                  </td>
                  <td>
                    <div class="flex space-x-1">
                      <button @click="selectedLog = log" class="btn btn-ghost btn-xs">
                        <Icon name="eye" size="xs" />
                      </button>
                      <button @click="copyLog(log)" class="btn btn-ghost btn-xs">
                        <Icon name="copy" size="xs" />
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Pagination -->
      <div v-if="totalPages > 1" class="flex justify-center items-center gap-2 mt-4">
        <button
          @click="prevPage"
          :disabled="currentPage === 1"
          class="btn btn-sm btn-outline"
        >
          <Icon name="chevron-left" size="sm" />
          Previous
        </button>

        <div class="flex items-center gap-1">
          <button
            v-for="page in getVisiblePages()"
            :key="page"
            @click="goToPage(page)"
            :class="[
              'btn btn-sm',
              page === currentPage ? 'btn-primary' : 'btn-outline'
            ]"
          >
            {{ page }}
          </button>
        </div>

        <button
          @click="nextPage"
          :disabled="currentPage === totalPages"
          class="btn btn-sm btn-outline"
        >
          Next
          <Icon name="chevron-right" size="sm" />
        </button>

        <div class="text-sm text-base-content/70 ml-4">
          Page {{ currentPage }} of {{ totalPages }} ({{ totalLogs }} total logs)
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="modal-action">
        <button @click="downloadLogs" class="btn btn-secondary">
          <Icon name="download" size="sm" class="mr-2" />
          Download Logs
        </button>
        <button @click="$emit('close')" class="btn">Close</button>
      </div>
    </div>
  </div>

  <!-- Log Details Modal -->
  <div v-if="selectedLog" class="modal modal-open">
    <div class="modal-box max-w-2xl">
      <h3 class="text-lg font-bold mb-4">Log Details</h3>
      <div class="space-y-4">
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="label">Timestamp</label>
            <div class="font-mono text-sm">{{ formatTime(selectedLog.timestamp) }}</div>
          </div>
          <div>
            <label class="label">Level</label>
            <div class="badge" :class="getLevelBadgeClass(selectedLog.level)">
              {{ selectedLog.level.toUpperCase() }}
            </div>
          </div>
          <div>
            <label class="label">Category</label>
            <div class="badge badge-outline">{{ selectedLog.category }}</div>
          </div>
          <div>
            <label class="label">Source</label>
            <div class="font-mono text-sm">{{ selectedLog.source || 'N/A' }}</div>
          </div>
        </div>
        <div>
          <label class="label">Message</label>
          <div class="bg-base-200 p-4 rounded-lg font-mono text-sm whitespace-pre-wrap">
            {{ selectedLog.message }}
          </div>
        </div>

        <div v-if="selectedLog.details && selectedLog.details !== '{}' && selectedLog.details !== 'null'">
          <label class="label">Details</label>
          <div class="bg-base-200 p-4 rounded-lg font-mono text-sm max-h-60 overflow-y-auto whitespace-pre-wrap">
            {{ selectedLog.details }}
          </div>
        </div>
        <div v-if="selectedLog.stackTrace">
          <label class="label">Stack Trace</label>
          <div class="bg-base-200 p-4 rounded-lg font-mono text-xs max-h-40 overflow-y-auto">
            <pre>{{ selectedLog.stackTrace }}</pre>
          </div>
        </div>
      </div>
      <div class="modal-action">
        <button @click="copyLogDetails" class="btn btn-secondary">Copy</button>
        <button @click="selectedLog = null" class="btn">Close</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import Icon from '@/components/common/Icon.vue'
import { useAuthStore } from '@/stores/auth'

defineEmits(['close'])

interface LogEntry {
  id: number
  timestamp: string
  level: 'error' | 'warn' | 'info' | 'debug'
  category: string
  message: string
  details?: string
  source?: string
  stackTrace?: string
}

const logs = ref<LogEntry[]>([])
const isLoading = ref(false)
const error = ref<string | null>(null)

// Pagination
const currentPage = ref(1)
const pageSize = ref(50)
const totalLogs = ref(0)
const totalPages = computed(() => Math.ceil(totalLogs.value / pageSize.value))

// Auth store
const authStore = useAuthStore()

const filters = ref({
  level: '',
  category: '',
  dateRange: 'today',
  search: ''
})

const selectedLog = ref<LogEntry | null>(null)
const autoRefresh = ref(false)
let refreshInterval: number | null = null

const logStats = computed(() => {
  return {
    errors: logs.value.filter(l => l.level === 'error').length,
    warnings: logs.value.filter(l => l.level === 'warn').length,
    info: logs.value.filter(l => l.level === 'info').length,
    debug: logs.value.filter(l => l.level === 'debug').length,
  }
})

const filteredLogs = computed(() => {
  let filtered = logs.value

  if (filters.value.level) {
    filtered = filtered.filter(log => log.level === filters.value.level)
  }

  if (filters.value.category) {
    filtered = filtered.filter(log => log.category === filters.value.category)
  }

  if (filters.value.search) {
    const search = filters.value.search.toLowerCase()
    filtered = filtered.filter(log => 
      log.message.toLowerCase().includes(search) ||
      log.details?.toLowerCase().includes(search)
    )
  }

  return filtered.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
})

const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleString()
}

const getLevelBadgeClass = (level: string) => {
  switch (level) {
    case 'error': return 'badge-error'
    case 'warn': return 'badge-warning'
    case 'info': return 'badge-info'
    case 'debug': return 'badge-success'
    default: return 'badge-ghost'
  }
}

const getLogRowClass = (level: string) => {
  switch (level) {
    case 'error': return 'bg-error/5'
    case 'warn': return 'bg-warning/5'
    default: return ''
  }
}

// API functions
const fetchLogs = async () => {
  try {
    isLoading.value = true
    error.value = null

    console.log('🔍 Debug - Auth state:', {
      isAuthenticated: authStore.isAuthenticated,
      isAdmin: authStore.isAdmin,
      hasToken: !!authStore.token,
      tokenPreview: authStore.token ? authStore.token.substring(0, 20) + '...' : 'null'
    })

    if (!authStore.isAuthenticated) {
      error.value = 'Authentication required. Please log in again.'
      isLoading.value = false
      return
    }

    if (!authStore.isAdmin) {
      error.value = 'Access denied. Admin privileges required.'
      isLoading.value = false
      return
    }

    const queryParams: Record<string, string> = {
      limit: pageSize.value.toString(),
      offset: ((currentPage.value - 1) * pageSize.value).toString()
    }
    if (filters.value.level) queryParams.level = filters.value.level
    if (filters.value.category) queryParams.category = filters.value.category

    const response = await fetch(`http://localhost:3001/api/v1/logs/system?${new URLSearchParams(queryParams)}`, {
      headers: {
        'Authorization': `Bearer ${authStore.token}`,
        'Content-Type': 'application/json'
      }
    })

    const data = await response.json()

    if (response.ok && data.success) {
      logs.value = data.data.logs || []
      totalLogs.value = data.data.total || 0
    } else {
      throw new Error(data.message || `HTTP ${response.status}`)
    }
  } catch (err: any) {
    console.error('Error fetching logs:', err)

    if (err.status === 401) {
      error.value = 'Authentication failed. Please log in again.'
    } else if (err.status === 403) {
      error.value = 'Access denied. Admin privileges required.'
    } else {
      error.value = err.message || 'Failed to fetch logs'
    }

    if (logs.value !== null) {
      logs.value = []
    }
  } finally {
    isLoading.value = false
  }
}

const applyFilters = () => {
  console.log('Applying filters:', filters.value)
  currentPage.value = 1 // Reset to first page when filtering
  fetchLogs()
}

const clearFilters = () => {
  filters.value = {
    level: '',
    category: '',
    dateRange: 'today',
    search: ''
  }
  currentPage.value = 1 // Reset to first page when clearing filters
  fetchLogs()
}

const refreshLogs = () => {
  console.log('Refreshing logs...')
  fetchLogs()
}

// Pagination functions
const goToPage = (page: number) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page
    fetchLogs()
  }
}

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
    fetchLogs()
  }
}

const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
    fetchLogs()
  }
}

const getVisiblePages = () => {
  const pages = []
  const maxVisible = 5
  const start = Math.max(1, currentPage.value - Math.floor(maxVisible / 2))
  const end = Math.min(totalPages.value, start + maxVisible - 1)

  for (let i = start; i <= end; i++) {
    pages.push(i)
  }

  return pages
}

const exportLogs = () => {
  console.log('Exporting logs...')
  alert('Log export initiated. Download will start shortly.')
}

const toggleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value

  if (autoRefresh.value) {
    refreshInterval = setInterval(fetchLogs, 10000) // Refresh every 10 seconds
  } else if (refreshInterval) {
    clearInterval(refreshInterval)
    refreshInterval = null
  }
}

const clearAllLogs = () => {
  if (confirm('Clear all logs? This action cannot be undone.')) {
    logs.value = []
  }
}



const copyLog = (log: LogEntry) => {
  const logText = `[${log.timestamp}] ${log.level.toUpperCase()} ${log.category}: ${log.message}`
  navigator.clipboard.writeText(logText)
  alert('Log entry copied to clipboard!')
}

const copyLogDetails = () => {
  if (selectedLog.value) {
    const details = JSON.stringify(selectedLog.value, null, 2)
    navigator.clipboard.writeText(details)
    alert('Log details copied to clipboard!')
  }
}

const downloadLogs = () => {
  console.log('Downloading logs...')
  alert('Log download started. File will be saved to your downloads folder.')
}

// Lifecycle hooks
onMounted(() => {
  fetchLogs()
})

onUnmounted(() => {
  if (refreshInterval) {
    clearInterval(refreshInterval)
  }
})
</script>
