<template>
  <div class="space-y-4">
    <!-- Filters -->
    <div class="card bg-base-100 shadow-sm border border-base-300">
      <div class="card-body p-4">
        <div class="flex flex-wrap gap-4 items-center">
          <div class="form-control">
            <label class="label">
              <span class="label-text">Level</span>
            </label>
            <select v-model="filters.level" class="select select-bordered select-sm">
              <option value="">All Levels</option>
              <option value="error">Error</option>
              <option value="warn">Warning</option>
              <option value="info">Info</option>
              <option value="debug">Debug</option>
            </select>
          </div>

          <div class="form-control">
            <label class="label">
              <span class="label-text">Type</span>
            </label>
            <select v-model="filters.type" class="select select-bordered select-sm">
              <option value="">All Types</option>
              <option value="worker">Worker</option>
              <option value="background_job">Background Job</option>
              <option value="job_scheduler">Job Scheduler</option>
              <option value="health_check">Health Check</option>
              <option value="stats_calculation">Stats Calculation</option>
            </select>
          </div>

          <div class="form-control">
            <label class="label">
              <span class="label-text">Limit</span>
            </label>
            <select v-model="filters.limit" class="select select-bordered select-sm">
              <option value="50">50 entries</option>
              <option value="100">100 entries</option>
              <option value="200">200 entries</option>
              <option value="500">500 entries</option>
            </select>
          </div>

          <div class="form-control">
            <label class="label">
              <span class="label-text">&nbsp;</span>
            </label>
            <button @click="applyFilters" class="btn btn-primary btn-sm">
              <Icon name="filter" size="sm" />
              Filter
            </button>
          </div>

          <div class="form-control">
            <label class="label">
              <span class="label-text">&nbsp;</span>
            </label>
            <button @click="clearFilters" class="btn btn-ghost btn-sm">
              Clear
            </button>
          </div>

          <div class="form-control">
            <label class="label">
              <span class="label-text">&nbsp;</span>
            </label>
            <div class="form-control">
              <label class="label cursor-pointer">
                <input 
                  v-model="autoRefresh" 
                  type="checkbox" 
                  class="toggle toggle-primary toggle-sm" 
                />
                <span class="label-text ml-2">Auto-refresh</span>
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Log Statistics -->
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
      <div class="stat bg-base-100 border border-base-300 rounded-lg">
        <div class="stat-title">Total Logs</div>
        <div class="stat-value">{{ logs.length }}</div>
      </div>
      <div class="stat bg-base-100 border border-base-300 rounded-lg">
        <div class="stat-title">Errors</div>
        <div class="stat-value text-error">{{ getLogCountByLevel('error') }}</div>
      </div>
      <div class="stat bg-base-100 border border-base-300 rounded-lg">
        <div class="stat-title">Warnings</div>
        <div class="stat-value text-warning">{{ getLogCountByLevel('warn') }}</div>
      </div>
      <div class="stat bg-base-100 border border-base-300 rounded-lg">
        <div class="stat-title">Info</div>
        <div class="stat-value text-info">{{ getLogCountByLevel('info') }}</div>
      </div>
    </div>

    <!-- Logs Display -->
    <div class="card bg-base-100 shadow-sm border border-base-300">
      <div class="card-body p-0">
        <div class="max-h-96 overflow-y-auto">
          <div v-if="loading" class="flex justify-center py-8">
            <span class="loading loading-spinner loading-md"></span>
          </div>
          
          <div v-else-if="filteredLogs.length === 0" class="text-center py-8 text-base-content/70">
            No logs found
          </div>
          
          <div v-else class="space-y-1 p-4">
            <div 
              v-for="log in filteredLogs" 
              :key="log.id"
              class="border border-base-300 rounded p-3 hover:bg-base-50 transition-colors"
              :class="getLogRowClass(log.level)"
            >
              <div class="flex items-start justify-between gap-4">
                <div class="flex-1 min-w-0">
                  <!-- Header -->
                  <div class="flex items-center gap-2 mb-1">
                    <div class="badge badge-sm" :class="getLevelBadgeClass(log.level)">
                      {{ log.level.toUpperCase() }}
                    </div>
                    <div v-if="log.type" class="badge badge-outline badge-sm">
                      {{ log.type }}
                    </div>
                    <div class="text-xs text-base-content/70 font-mono">
                      {{ formatDateTime(log.timestamp) }}
                    </div>
                  </div>
                  
                  <!-- Message -->
                  <div class="text-sm font-mono break-words">
                    {{ log.message }}
                  </div>
                  
                  <!-- Metadata -->
                  <div v-if="log.metadata && Object.keys(log.metadata).length > 0" class="mt-2">
                    <button 
                      @click="toggleMetadata(log.id)"
                      class="text-xs text-primary hover:underline"
                    >
                      {{ expandedLogs.has(log.id) ? 'Hide' : 'Show' }} metadata
                    </button>
                    <div v-if="expandedLogs.has(log.id)" class="mt-1">
                      <pre class="text-xs bg-base-200 p-2 rounded overflow-x-auto">{{ JSON.stringify(log.metadata, null, 2) }}</pre>
                    </div>
                  </div>
                </div>
                
                <div class="flex-shrink-0">
                  <button 
                    @click="copyLog(log)" 
                    class="btn btn-ghost btn-xs"
                    title="Copy log"
                  >
                    <Icon name="clipboard" size="xs" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Auto-refresh indicator -->
    <div v-if="autoRefresh" class="text-center text-sm text-base-content/70">
      <Icon name="refresh" size="sm" class="inline mr-1 animate-spin" />
      Auto-refreshing every 10 seconds
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import Icon from '@/components/common/Icon.vue'

// Props
const props = defineProps({
  logs: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['refresh'])

// State
const filters = ref({
  level: '',
  type: '',
  limit: '100'
})

const autoRefresh = ref(false)
const expandedLogs = ref(new Set())
let refreshInterval = null

// Computed
const filteredLogs = computed(() => {
  let filtered = props.logs

  if (filters.value.level) {
    filtered = filtered.filter(log => log.level === filters.value.level)
  }

  if (filters.value.type) {
    filtered = filtered.filter(log => log.type === filters.value.type)
  }

  return filtered.slice(0, parseInt(filters.value.limit))
})

// Methods
const getLevelBadgeClass = (level) => {
  switch (level.toLowerCase()) {
    case 'error': return 'badge-error'
    case 'warn': return 'badge-warning'
    case 'info': return 'badge-info'
    case 'debug': return 'badge-ghost'
    default: return 'badge-primary'
  }
}

const getLogRowClass = (level) => {
  switch (level.toLowerCase()) {
    case 'error': return 'border-l-4 border-l-error bg-error/5'
    case 'warn': return 'border-l-4 border-l-warning bg-warning/5'
    case 'info': return 'border-l-4 border-l-info bg-info/5'
    default: return ''
  }
}

const getLogCountByLevel = (level) => {
  return props.logs.filter(log => log.level === level).length
}

const formatDateTime = (dateString) => {
  return new Date(dateString).toLocaleString()
}

const toggleMetadata = (logId) => {
  if (expandedLogs.value.has(logId)) {
    expandedLogs.value.delete(logId)
  } else {
    expandedLogs.value.add(logId)
  }
}

const copyLog = async (log) => {
  try {
    const logText = `[${log.timestamp}] ${log.level.toUpperCase()}: ${log.message}`
    await navigator.clipboard.writeText(logText)
    // You could show a toast notification here
  } catch (error) {
    console.error('Failed to copy log:', error)
  }
}

const applyFilters = () => {
  emit('refresh')
}

const clearFilters = () => {
  filters.value = {
    level: '',
    type: '',
    limit: '100'
  }
  emit('refresh')
}

// Watch auto-refresh
watch(autoRefresh, (enabled) => {
  if (enabled) {
    refreshInterval = setInterval(() => {
      emit('refresh')
    }, 10000) // Refresh every 10 seconds
  } else {
    if (refreshInterval) {
      clearInterval(refreshInterval)
      refreshInterval = null
    }
  }
})

// Cleanup
onUnmounted(() => {
  if (refreshInterval) {
    clearInterval(refreshInterval)
  }
})
</script>
