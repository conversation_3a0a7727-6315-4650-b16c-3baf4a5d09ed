<template>
  <div class="modal modal-open">
    <div class="modal-box max-w-2xl">
      <h3 class="text-lg font-bold mb-4">Enqueue Background Job</h3>
      
      <form @submit.prevent="enqueueJob" class="space-y-4">
        <!-- Job Type -->
        <div class="form-control">
          <label class="label">
            <span class="label-text font-semibold">Job Type</span>
          </label>
          <select v-model="form.jobType" class="select select-bordered" required>
            <option value="">Select job type...</option>
            <optgroup label="Statistics">
              <option value="calculate-daily-stats">Calculate Daily Stats</option>
              <option value="calculate-weekly-stats">Calculate Weekly Stats</option>
              <option value="calculate-monthly-stats">Calculate Monthly Stats</option>
              <option value="cleanup-old-stats">Cleanup Old Stats</option>
            </optgroup>
            <optgroup label="Health Checks">
              <option value="check-database-health">Database Health Check</option>
              <option value="check-api-health">API Health Check</option>
              <option value="check-frontend-health">Frontend Health Check</option>
              <option value="check-memory-usage">Memory Usage Check</option>
              <option value="check-external-services">External Services Check</option>
              <option value="comprehensive-health-check">Comprehensive Health Check</option>
            </optgroup>
            <optgroup label="Cleanup Tasks">
              <option value="cleanup-old-logs">Cleanup Old Logs</option>
              <option value="cleanup-expired-sessions">Cleanup Expired Sessions</option>
              <option value="cleanup-old-jobs">Cleanup Old Jobs</option>
              <option value="optimize-database">Optimize Database</option>
              <option value="backup-critical-data">Backup Critical Data</option>
            </optgroup>
            <optgroup label="Email Tasks">
              <option value="send-daily-digest">Send Daily Digest</option>
              <option value="send-weekly-report">Send Weekly Report</option>
              <option value="send-alert-notification">Send Alert Notification</option>
            </optgroup>
          </select>
        </div>

        <!-- Job Description -->
        <div v-if="form.jobType" class="alert alert-info">
          <Icon name="info" size="md" />
          <div>
            <div class="font-semibold">{{ getJobDisplayName(form.jobType) }}</div>
            <div class="text-sm">{{ getJobDescription(form.jobType) }}</div>
          </div>
        </div>

        <!-- Priority -->
        <div class="form-control">
          <label class="label">
            <span class="label-text font-semibold">Priority</span>
          </label>
          <select v-model="form.priority" class="select select-bordered">
            <option value="0">Low (0)</option>
            <option value="1">Low (1)</option>
            <option value="2">Low (2)</option>
            <option value="3">Low (3)</option>
            <option value="4">Medium (4)</option>
            <option value="5">Medium (5)</option>
            <option value="6">Medium (6)</option>
            <option value="7">High (7)</option>
            <option value="8">High (8)</option>
            <option value="9">High (9)</option>
            <option value="10">Critical (10)</option>
          </select>
          <div class="label">
            <span class="label-text-alt">Higher priority jobs are processed first</span>
          </div>
        </div>

        <!-- Job Data -->
        <div class="form-control">
          <label class="label">
            <span class="label-text font-semibold">Job Data (JSON)</span>
          </label>
          <textarea 
            v-model="form.jobData" 
            class="textarea textarea-bordered h-32 font-mono text-sm"
            placeholder="Enter job-specific data as JSON (optional)"
          ></textarea>
          <div class="label">
            <span class="label-text-alt">Optional configuration data for the job</span>
          </div>
        </div>

        <!-- Common Job Data Templates -->
        <div v-if="getJobDataTemplate(form.jobType)" class="card bg-base-200">
          <div class="card-body p-4">
            <h4 class="font-semibold text-sm mb-2">Common Configuration:</h4>
            <button 
              type="button"
              @click="useTemplate"
              class="btn btn-outline btn-sm"
            >
              Use Template
            </button>
            <pre class="text-xs mt-2 overflow-x-auto">{{ getJobDataTemplate(form.jobType) }}</pre>
          </div>
        </div>

        <!-- JSON Validation -->
        <div v-if="jsonError" class="alert alert-error">
          <Icon name="exclamation" size="md" />
          <div>
            <div class="font-semibold">Invalid JSON</div>
            <div class="text-sm">{{ jsonError }}</div>
          </div>
        </div>

        <!-- Actions -->
        <div class="modal-action">
          <button type="button" @click="$emit('close')" class="btn btn-ghost">
            Cancel
          </button>
          <button 
            type="submit" 
            class="btn btn-primary" 
            :disabled="!form.jobType || isSubmitting || !!jsonError"
          >
            <span v-if="isSubmitting" class="loading loading-spinner loading-sm"></span>
            {{ isSubmitting ? 'Enqueuing...' : 'Enqueue Job' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { apiService } from '@/services/api'
import Icon from '@/components/common/Icon.vue'

// Emits
const emit = defineEmits(['close', 'job-enqueued'])

// State
const isSubmitting = ref(false)
const form = ref({
  jobType: '',
  priority: 5,
  jobData: ''
})

// Computed
const jsonError = computed(() => {
  if (!form.value.jobData.trim()) return null
  
  try {
    JSON.parse(form.value.jobData)
    return null
  } catch (error) {
    return error.message
  }
})

// Methods
const getJobDisplayName = (jobType) => {
  const names = {
    'calculate-daily-stats': 'Calculate Daily Statistics',
    'calculate-weekly-stats': 'Calculate Weekly Statistics',
    'calculate-monthly-stats': 'Calculate Monthly Statistics',
    'cleanup-old-stats': 'Cleanup Old Statistics',
    'check-database-health': 'Database Health Check',
    'check-api-health': 'API Health Check',
    'check-frontend-health': 'Frontend Health Check',
    'check-memory-usage': 'Memory Usage Check',
    'check-external-services': 'External Services Check',
    'comprehensive-health-check': 'Comprehensive Health Check',
    'cleanup-old-logs': 'Cleanup Old Logs',
    'cleanup-expired-sessions': 'Cleanup Expired Sessions',
    'cleanup-old-jobs': 'Cleanup Old Jobs',
    'optimize-database': 'Optimize Database',
    'backup-critical-data': 'Backup Critical Data',
    'send-daily-digest': 'Send Daily Digest',
    'send-weekly-report': 'Send Weekly Report',
    'send-alert-notification': 'Send Alert Notification'
  }
  return names[jobType] || jobType
}

const getJobDescription = (jobType) => {
  const descriptions = {
    'calculate-daily-stats': 'Calculate and cache daily system statistics including user activity, log counts, and connection metrics.',
    'calculate-weekly-stats': 'Generate weekly summary reports and analytics.',
    'calculate-monthly-stats': 'Create monthly system reports and trend analysis.',
    'cleanup-old-stats': 'Remove expired cached statistics to free up database space.',
    'check-database-health': 'Test database connectivity, query performance, and connection pool status.',
    'check-api-health': 'Verify API endpoint availability and response times.',
    'check-frontend-health': 'Test frontend application accessibility and response times.',
    'check-memory-usage': 'Monitor memory consumption, heap usage, and detect potential memory leaks.',
    'check-external-services': 'Test connectivity to external APIs and third-party services.',
    'comprehensive-health-check': 'Run complete system health check (database, API, frontend) and send email alerts if issues are found.',
    'cleanup-old-logs': 'Remove old log entries based on retention policy.',
    'cleanup-expired-sessions': 'Clean up expired user sessions from the database.',
    'cleanup-old-jobs': 'Remove completed background jobs older than specified days.',
    'optimize-database': 'Optimize database tables and rebuild indexes for better performance.',
    'backup-critical-data': 'Create backup of critical system data and configuration.',
    'send-daily-digest': 'Send daily system summary email to administrators.',
    'send-weekly-report': 'Send comprehensive weekly report to administrators.',
    'send-alert-notification': 'Send critical system alert notifications.'
  }
  return descriptions[jobType] || 'Execute the specified background job.'
}

const getJobDataTemplate = (jobType) => {
  const templates = {
    'cleanup-old-logs': '{\n  "daysToKeep": 30\n}',
    'cleanup-old-jobs': '{\n  "daysToKeep": 7\n}',
    'cleanup-old-stats': '{\n  "daysToKeep": 30\n}',
    'send-alert-notification': '{\n  "alertType": "system_error",\n  "message": "Critical system issue detected",\n  "severity": "high",\n  "details": {}\n}',
    'backup-critical-data': '{\n  "includeUserData": true,\n  "includeSystemConfig": true\n}'
  }
  return templates[jobType] || null
}

const useTemplate = () => {
  const template = getJobDataTemplate(form.value.jobType)
  if (template) {
    form.value.jobData = template
  }
}

const enqueueJob = async () => {
  if (isSubmitting.value) return
  
  isSubmitting.value = true
  
  try {
    let jobData = {}
    
    if (form.value.jobData.trim()) {
      try {
        jobData = JSON.parse(form.value.jobData)
      } catch (error) {
        throw new Error('Invalid JSON in job data')
      }
    }
    
    const response = await apiService.post('/worker/jobs', {
      jobType: form.value.jobType,
      jobData,
      priority: parseInt(form.value.priority)
    })
    
    if (response.success) {
      emit('job-enqueued')
    } else {
      throw new Error(response.message || 'Failed to enqueue job')
    }
    
  } catch (error) {
    console.error('Error enqueuing job:', error)
    alert(`Failed to enqueue job: ${error.message}`)
  } finally {
    isSubmitting.value = false
  }
}

// Watch job type changes to clear job data
watch(() => form.value.jobType, () => {
  form.value.jobData = ''
})
</script>
