<template>
  <div class="space-y-6">
    <!-- Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <!-- Users Statistics -->
      <div class="card bg-base-100 shadow-sm border border-base-300">
        <div class="card-body">
          <h3 class="card-title text-lg flex items-center">
            <Icon name="users" size="md" class="text-primary" />
            User Statistics
          </h3>
          
          <div v-if="loading" class="flex justify-center py-8">
            <span class="loading loading-spinner loading-md"></span>
          </div>
          
          <div v-else-if="stats.users" class="space-y-3">
            <div class="flex justify-between items-center">
              <span class="text-sm text-base-content/70">Total Users</span>
              <span class="font-semibold text-lg">{{ stats.users.total || 0 }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-sm text-base-content/70">Active (24h)</span>
              <span class="font-semibold text-success">{{ stats.users.active?.last24h || 0 }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-sm text-base-content/70">Active (7d)</span>
              <span class="font-semibold text-info">{{ stats.users.active?.last7d || 0 }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-sm text-base-content/70">New (24h)</span>
              <span class="font-semibold text-warning">{{ stats.users.new?.last24h || 0 }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-sm text-base-content/70">Active Sessions</span>
              <span class="font-semibold">{{ stats.users.sessions?.active || 0 }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Logs Statistics -->
      <div class="card bg-base-100 shadow-sm border border-base-300">
        <div class="card-body">
          <h3 class="card-title text-lg flex items-center">
            <Icon name="document-text" size="md" class="text-secondary" />
            Log Statistics
          </h3>
          
          <div v-if="loading" class="flex justify-center py-8">
            <span class="loading loading-spinner loading-md"></span>
          </div>
          
          <div v-else-if="stats.logs" class="space-y-3">
            <div class="flex justify-between items-center">
              <span class="text-sm text-base-content/70">Total Logs</span>
              <span class="font-semibold text-lg">{{ formatNumber(stats.logs.total) || 0 }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-sm text-base-content/70">Recent (24h)</span>
              <span class="font-semibold text-info">{{ formatNumber(stats.logs.recent?.last24h) || 0 }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-sm text-base-content/70">Recent (7d)</span>
              <span class="font-semibold text-primary">{{ formatNumber(stats.logs.recent?.last7d) || 0 }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-sm text-base-content/70">Errors (24h)</span>
              <span class="font-semibold text-error">{{ stats.logs.errors?.last24h || 0 }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-sm text-base-content/70">Warnings (24h)</span>
              <span class="font-semibold text-warning">{{ stats.logs.warnings?.last24h || 0 }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Connections Statistics -->
      <div class="card bg-base-100 shadow-sm border border-base-300">
        <div class="card-body">
          <h3 class="card-title text-lg flex items-center">
            <Icon name="signal" size="md" class="text-accent" />
            Connection Statistics
          </h3>
          
          <div v-if="loading" class="flex justify-center py-8">
            <span class="loading loading-spinner loading-md"></span>
          </div>
          
          <div v-else-if="stats.connections" class="space-y-3">
            <div class="flex justify-between items-center">
              <span class="text-sm text-base-content/70">Current</span>
              <span class="font-semibold text-lg text-success">{{ stats.connections.current || 0 }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-sm text-base-content/70">Peak (24h)</span>
              <span class="font-semibold text-info">{{ stats.connections.peak24h || 0 }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-sm text-base-content/70">User Connections</span>
              <span class="font-semibold text-primary">{{ stats.connections.userConnections || 0 }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-sm text-base-content/70">Admin Connections</span>
              <span class="font-semibold text-warning">{{ stats.connections.adminConnections || 0 }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-sm text-base-content/70">Total Rooms</span>
              <span class="font-semibold">{{ stats.connections.totalRooms || 0 }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Detailed Statistics -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Log Breakdown -->
      <div class="card bg-base-100 shadow-sm border border-base-300">
        <div class="card-body">
          <h3 class="card-title text-lg">Log Breakdown by Level</h3>
          
          <div v-if="loading" class="flex justify-center py-8">
            <span class="loading loading-spinner loading-md"></span>
          </div>
          
          <div v-else-if="stats.logs?.byLevel" class="space-y-3">
            <div 
              v-for="(count, level) in stats.logs.byLevel" 
              :key="level"
              class="flex justify-between items-center"
            >
              <div class="flex items-center gap-2">
                <div class="w-3 h-3 rounded-full" :class="getLevelColor(level)"></div>
                <span class="capitalize">{{ level }}</span>
              </div>
              <span class="font-semibold">{{ formatNumber(count) }}</span>
            </div>
          </div>
          
          <div v-else class="text-center py-8 text-base-content/70">
            No log level data available
          </div>
        </div>
      </div>

      <!-- Log Types -->
      <div class="card bg-base-100 shadow-sm border border-base-300">
        <div class="card-body">
          <h3 class="card-title text-lg">Top Log Types (24h)</h3>
          
          <div v-if="loading" class="flex justify-center py-8">
            <span class="loading loading-spinner loading-md"></span>
          </div>
          
          <div v-else-if="stats.logs?.byType?.length" class="space-y-3">
            <div 
              v-for="(item, index) in stats.logs.byType.slice(0, 8)" 
              :key="item.type"
              class="flex justify-between items-center"
            >
              <div class="flex items-center gap-2">
                <div class="w-6 h-6 rounded bg-primary/10 text-primary text-xs flex items-center justify-center font-bold">
                  {{ index + 1 }}
                </div>
                <span class="text-sm">{{ item.type || 'Unknown' }}</span>
              </div>
              <span class="font-semibold">{{ formatNumber(item.count) }}</span>
            </div>
          </div>
          
          <div v-else class="text-center py-8 text-base-content/70">
            No log type data available
          </div>
        </div>
      </div>
    </div>

    <!-- Job Statistics -->
    <div class="card bg-base-100 shadow-sm border border-base-300">
      <div class="card-body">
        <h3 class="card-title text-lg flex items-center">
          <Icon name="cog-6-tooth" size="md" class="text-info" />
          Background Job Statistics
        </h3>
        
        <div v-if="loading" class="flex justify-center py-8">
          <span class="loading loading-spinner loading-md"></span>
        </div>
        
        <div v-else-if="stats.jobs" class="grid grid-cols-2 md:grid-cols-5 gap-4">
          <div class="text-center">
            <div class="text-2xl font-bold text-warning">{{ stats.jobs.pending || 0 }}</div>
            <div class="text-sm text-base-content/70">Pending</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-info">{{ stats.jobs.processing || 0 }}</div>
            <div class="text-sm text-base-content/70">Processing</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-success">{{ stats.jobs.completed || 0 }}</div>
            <div class="text-sm text-base-content/70">Completed</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-error">{{ stats.jobs.failed || 0 }}</div>
            <div class="text-sm text-base-content/70">Failed</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold">{{ stats.jobs.total || 0 }}</div>
            <div class="text-sm text-base-content/70">Total</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Last Updated -->
    <div class="text-center text-sm text-base-content/70">
      <Icon name="clock" size="sm" class="inline mr-1" />
      Last updated: {{ stats.lastUpdated ? formatDateTime(stats.lastUpdated) : 'Never' }}
    </div>
  </div>
</template>

<script setup>
import Icon from '@/components/common/Icon.vue'

// Props
const props = defineProps({
  stats: {
    type: Object,
    default: () => ({})
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['refresh'])

// Methods
const formatNumber = (num) => {
  if (!num) return '0'
  return new Intl.NumberFormat().format(num)
}

const formatDateTime = (dateString) => {
  if (!dateString) return 'Never'
  return new Date(dateString).toLocaleString()
}

const getLevelColor = (level) => {
  switch (level.toLowerCase()) {
    case 'error': return 'bg-error'
    case 'warn': return 'bg-warning'
    case 'info': return 'bg-info'
    case 'debug': return 'bg-base-content/30'
    default: return 'bg-primary'
  }
}
</script>
