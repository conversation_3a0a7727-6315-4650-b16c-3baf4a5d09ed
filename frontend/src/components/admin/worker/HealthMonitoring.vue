<template>
  <div class="space-y-6">
    <!-- Overall Health Status -->
    <div class="card bg-base-100 shadow-sm border border-base-300">
      <div class="card-body">
        <div class="flex justify-between items-center">
          <div>
            <h3 class="card-title text-xl flex items-center">
              <Icon :name="overallHealthIcon" size="lg" :class="overallHealthColor" />
              System Health Overview
            </h3>
            <p class="text-base-content/70 mt-1">{{ health.message || 'No health data available' }}</p>
          </div>
          <div class="text-right">
            <div class="text-3xl font-bold" :class="overallHealthColor">
              {{ health.status || 'Unknown' }}
            </div>
            <button @click="runHealthChecks" class="btn btn-primary btn-sm mt-2" :disabled="loading">
              <Icon name="play" size="sm" />
              Run Checks
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Health Summary -->
    <div v-if="health.summary" class="grid grid-cols-2 md:grid-cols-4 gap-4">
      <div class="stat bg-base-100 border border-base-300 rounded-lg">
        <div class="stat-title">Healthy</div>
        <div class="stat-value text-success">{{ health.summary.healthy || 0 }}</div>
      </div>
      <div class="stat bg-base-100 border border-base-300 rounded-lg">
        <div class="stat-title">Degraded</div>
        <div class="stat-value text-warning">{{ health.summary.degraded || 0 }}</div>
      </div>
      <div class="stat bg-base-100 border border-base-300 rounded-lg">
        <div class="stat-title">Unhealthy</div>
        <div class="stat-value text-error">{{ health.summary.unhealthy || 0 }}</div>
      </div>
      <div class="stat bg-base-100 border border-base-300 rounded-lg">
        <div class="stat-title">Total Checks</div>
        <div class="stat-value">{{ health.summary.total || 0 }}</div>
      </div>
    </div>

    <!-- Individual Health Checks -->
    <div class="card bg-base-100 shadow-sm border border-base-300">
      <div class="card-body">
        <h3 class="card-title text-lg mb-4">Individual Health Checks</h3>
        
        <div v-if="loading" class="flex justify-center py-8">
          <span class="loading loading-spinner loading-md"></span>
        </div>
        
        <div v-else-if="health.checks?.length" class="space-y-4">
          <div 
            v-for="check in health.checks" 
            :key="check.check_name"
            class="border border-base-300 rounded-lg p-4"
          >
            <div class="flex justify-between items-start">
              <div class="flex-1">
                <div class="flex items-center gap-3 mb-2">
                  <Icon :name="getCheckIcon(check.check_name)" size="md" class="text-primary" />
                  <div>
                    <h4 class="font-semibold">{{ formatCheckName(check.check_name) }}</h4>
                    <p class="text-sm text-base-content/70">{{ getCheckDescription(check.check_name) }}</p>
                  </div>
                </div>
                
                <div class="flex items-center gap-4 mt-3">
                  <div class="badge" :class="getStatusBadgeClass(check.status)">
                    {{ check.status }}
                  </div>
                  
                  <div v-if="check.response_time_ms" class="text-sm text-base-content/70">
                    <Icon name="clock" size="sm" class="inline mr-1" />
                    {{ check.response_time_ms }}ms
                  </div>
                  
                  <div class="text-sm text-base-content/70">
                    <Icon name="calendar" size="sm" class="inline mr-1" />
                    {{ formatDateTime(check.latest_check) }}
                  </div>
                </div>
              </div>
              
              <button 
                @click="viewCheckDetails(check)" 
                class="btn btn-ghost btn-sm"
                title="View Details"
              >
                <Icon name="eye" size="sm" />
              </button>
            </div>
          </div>
        </div>
        
        <div v-else class="text-center py-8 text-base-content/70">
          No health check data available
        </div>
      </div>
    </div>

    <!-- Health Check Details Modal -->
    <div v-if="selectedCheck" class="modal modal-open">
      <div class="modal-box max-w-2xl">
        <h3 class="text-lg font-bold mb-4">
          {{ formatCheckName(selectedCheck.check_name) }} Details
        </h3>
        
        <div class="space-y-4">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="label label-text font-semibold">Status</label>
              <div class="badge badge-lg" :class="getStatusBadgeClass(selectedCheck.status)">
                {{ selectedCheck.status }}
              </div>
            </div>
            <div v-if="selectedCheck.response_time_ms">
              <label class="label label-text font-semibold">Response Time</label>
              <div class="text-sm bg-base-200 p-2 rounded">{{ selectedCheck.response_time_ms }}ms</div>
            </div>
          </div>

          <div>
            <label class="label label-text font-semibold">Last Checked</label>
            <div class="text-sm bg-base-200 p-2 rounded">
              {{ formatDateTime(selectedCheck.latest_check) }}
            </div>
          </div>

          <div v-if="selectedCheck.details">
            <label class="label label-text font-semibold">Details</label>
            <pre class="bg-base-200 p-3 rounded text-sm overflow-x-auto max-h-60">{{ JSON.stringify(selectedCheck.details, null, 2) }}</pre>
          </div>
        </div>

        <div class="modal-action">
          <button @click="selectedCheck = null" class="btn">Close</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import Icon from '@/components/common/Icon.vue'

// Props
const props = defineProps({
  health: {
    type: Object,
    default: () => ({})
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['refresh', 'run-checks'])

// State
const selectedCheck = ref(null)

// Computed
const overallHealthColor = computed(() => {
  const status = props.health.status
  if (status === 'healthy') return 'text-success'
  if (status === 'degraded') return 'text-warning'
  if (status === 'unhealthy') return 'text-error'
  return 'text-base-content/70'
})

const overallHealthIcon = computed(() => {
  const status = props.health.status
  if (status === 'healthy') return 'heart'
  if (status === 'degraded') return 'exclamation-triangle'
  if (status === 'unhealthy') return 'close'
  return 'question'
})

// Methods
const getStatusBadgeClass = (status) => {
  switch (status) {
    case 'healthy': return 'badge-success'
    case 'degraded': return 'badge-warning'
    case 'unhealthy': return 'badge-error'
    default: return 'badge-ghost'
  }
}

const getCheckIcon = (checkName) => {
  if (checkName.includes('database')) return 'circle-stack'
  if (checkName.includes('memory')) return 'cpu-chip'
  if (checkName.includes('api')) return 'globe-alt'
  if (checkName.includes('socket')) return 'signal'
  if (checkName.includes('external')) return 'cloud'
  return 'heart'
}

const formatCheckName = (checkName) => {
  return checkName.split('-').map(word => 
    word.charAt(0).toUpperCase() + word.slice(1)
  ).join(' ')
}

const getCheckDescription = (checkName) => {
  const descriptions = {
    'database-connectivity': 'Tests database connection and query performance',
    'database-performance': 'Monitors database response times and connection pool',
    'api-health': 'Checks API endpoint availability and response times',
    'memory-usage': 'Monitors memory consumption and heap usage',
    'cpu-usage': 'Tracks CPU utilization and performance',
    'socket-server': 'Verifies WebSocket server status and connections',
    'external-services': 'Tests connectivity to external APIs and services',
    'disk-space': 'Monitors available disk space and storage usage'
  }
  return descriptions[checkName] || 'System health check'
}

const formatDateTime = (dateString) => {
  if (!dateString) return 'Never'
  return new Date(dateString).toLocaleString()
}

const viewCheckDetails = (check) => {
  selectedCheck.value = check
}

const runHealthChecks = () => {
  emit('run-checks')
}
</script>
