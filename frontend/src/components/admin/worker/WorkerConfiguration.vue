<template>
  <div class="space-y-6">
    <!-- Configuration Overview -->
    <div class="card bg-base-100 shadow-sm border border-base-300">
      <div class="card-body">
        <h3 class="card-title text-lg mb-4">Worker Configuration</h3>
        
        <div v-if="loading" class="flex justify-center py-8">
          <span class="loading loading-spinner loading-md"></span>
        </div>
        
        <div v-else-if="config" class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Performance Settings -->
          <div class="space-y-4">
            <h4 class="font-semibold text-base flex items-center">
              <Icon name="cog" size="md" class="mr-2 text-primary" />
              Performance Settings
            </h4>
            
            <div class="space-y-3">
              <div class="flex justify-between items-center p-3 bg-base-200 rounded">
                <div>
                  <div class="font-medium">Max Concurrent Jobs</div>
                  <div class="text-sm text-base-content/70">Maximum jobs processed simultaneously</div>
                </div>
                <div class="badge badge-primary badge-lg">{{ config.maxConcurrentJobs }}</div>
              </div>
              
              <div class="flex justify-between items-center p-3 bg-base-200 rounded">
                <div>
                  <div class="font-medium">Poll Interval</div>
                  <div class="text-sm text-base-content/70">How often to check for new jobs</div>
                </div>
                <div class="badge badge-secondary badge-lg">{{ formatInterval(config.pollInterval) }}</div>
              </div>
              
              <div class="flex justify-between items-center p-3 bg-base-200 rounded">
                <div>
                  <div class="font-medium">Max Retries</div>
                  <div class="text-sm text-base-content/70">Maximum retry attempts for failed jobs</div>
                </div>
                <div class="badge badge-warning badge-lg">{{ config.maxRetries }}</div>
              </div>
              
              <div class="flex justify-between items-center p-3 bg-base-200 rounded">
                <div>
                  <div class="font-medium">Retry Delay</div>
                  <div class="text-sm text-base-content/70">Delay between retry attempts</div>
                </div>
                <div class="badge badge-info badge-lg">{{ formatInterval(config.retryDelay) }}</div>
              </div>
            </div>
          </div>

          <!-- Environment Settings -->
          <div class="space-y-4">
            <h4 class="font-semibold text-base flex items-center">
              <Icon name="server" size="md" class="mr-2 text-secondary" />
              Environment Settings
            </h4>
            
            <div class="space-y-3">
              <div class="flex justify-between items-center p-3 bg-base-200 rounded">
                <div>
                  <div class="font-medium">Environment</div>
                  <div class="text-sm text-base-content/70">Current runtime environment</div>
                </div>
                <div class="badge" :class="getEnvironmentBadgeClass(config.environment)">
                  {{ config.environment }}
                </div>
              </div>
              
              <div class="flex justify-between items-center p-3 bg-base-200 rounded">
                <div>
                  <div class="font-medium">Timezone</div>
                  <div class="text-sm text-base-content/70">Scheduled job timezone</div>
                </div>
                <div class="badge badge-accent badge-lg">{{ config.timezone }}</div>
              </div>
              
              <div class="flex justify-between items-center p-3 bg-base-200 rounded">
                <div>
                  <div class="font-medium">Debug Mode</div>
                  <div class="text-sm text-base-content/70">Enhanced logging enabled</div>
                </div>
                <div class="badge" :class="config.debugMode ? 'badge-warning' : 'badge-ghost'">
                  {{ config.debugMode ? 'Enabled' : 'Disabled' }}
                </div>
              </div>
              
              <div class="flex justify-between items-center p-3 bg-base-200 rounded">
                <div>
                  <div class="font-medium">Socket Debug</div>
                  <div class="text-sm text-base-content/70">Socket.io debug logging</div>
                </div>
                <div class="badge" :class="config.socketDebug ? 'badge-warning' : 'badge-ghost'">
                  {{ config.socketDebug ? 'Enabled' : 'Disabled' }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Scheduled Jobs Configuration -->
    <div class="card bg-base-100 shadow-sm border border-base-300">
      <div class="card-body">
        <h3 class="card-title text-lg mb-4">Scheduled Jobs</h3>
        
        <div class="overflow-x-auto">
          <table class="table table-zebra">
            <thead>
              <tr>
                <th>Job Type</th>
                <th>Schedule</th>
                <th>Description</th>
                <th>Priority</th>
                <th>Status</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="job in scheduledJobs" :key="job.name">
                <td>
                  <div class="font-medium">{{ job.displayName }}</div>
                  <div class="text-xs text-base-content/70">{{ job.jobType }}</div>
                </td>
                <td>
                  <div class="font-mono text-sm">{{ job.schedule }}</div>
                  <div class="text-xs text-base-content/70">{{ job.humanSchedule }}</div>
                </td>
                <td class="text-sm">{{ job.description }}</td>
                <td>
                  <div class="badge" :class="getPriorityBadgeClass(job.priority)">
                    {{ job.priority }}
                  </div>
                </td>
                <td>
                  <div class="badge badge-success">Active</div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Performance Recommendations -->
    <div class="card bg-base-100 shadow-sm border border-base-300">
      <div class="card-body">
        <h3 class="card-title text-lg mb-4 flex items-center">
          <Icon name="lightbulb" size="md" class="mr-2 text-warning" />
          Performance Recommendations
        </h3>
        
        <div class="space-y-3">
          <div v-if="config.maxConcurrentJobs > 10" class="alert alert-warning">
            <Icon name="exclamation-triangle" size="md" />
            <div>
              <div class="font-semibold">High Concurrent Jobs</div>
              <div class="text-sm">Consider reducing max concurrent jobs if experiencing high memory usage.</div>
            </div>
          </div>

          <div v-if="config.pollInterval < 3000" class="alert alert-info">
            <Icon name="info" size="md" />
            <div>
              <div class="font-semibold">Frequent Polling</div>
              <div class="text-sm">Short poll intervals may increase database load. Consider increasing if jobs are not time-critical.</div>
            </div>
          </div>

          <div v-if="config.debugMode && config.environment === 'production'" class="alert alert-error">
            <Icon name="exclamation" size="md" />
            <div>
              <div class="font-semibold">Debug Mode in Production</div>
              <div class="text-sm">Debug mode should be disabled in production for better performance.</div>
            </div>
          </div>

          <div v-if="!hasRecommendations" class="alert alert-success">
            <Icon name="check-circle" size="md" />
            <div>
              <div class="font-semibold">Configuration Looks Good</div>
              <div class="text-sm">Your worker configuration appears to be optimized for your environment.</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import Icon from '@/components/common/Icon.vue'

// Props
const props = defineProps({
  config: {
    type: Object,
    default: () => ({})
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['refresh'])

// Scheduled jobs data
const scheduledJobs = [
  {
    name: 'calculate-stats',
    displayName: 'Calculate Statistics',
    jobType: 'calculate-daily-stats',
    schedule: '*/15 * * * *',
    humanSchedule: 'Every 15 minutes',
    description: 'Calculate and cache daily system statistics',
    priority: 5
  },
  {
    name: 'health-checks',
    displayName: 'Health Checks',
    jobType: 'check-database-health',
    schedule: '*/5 * * * *',
    humanSchedule: 'Every 5 minutes',
    description: 'Monitor database connectivity and performance',
    priority: 7
  },
  {
    name: 'comprehensive-health-check',
    displayName: 'Comprehensive Health Check',
    jobType: 'comprehensive-health-check',
    schedule: '*/10 * * * *',
    humanSchedule: 'Every 10 minutes',
    description: 'Complete system health check with email alerts',
    priority: 8
  },
  {
    name: 'memory-check',
    displayName: 'Memory Check',
    jobType: 'check-memory-usage',
    schedule: '*/10 * * * *',
    humanSchedule: 'Every 10 minutes',
    description: 'Monitor memory usage and performance',
    priority: 6
  },
  {
    name: 'cleanup-old-logs',
    displayName: 'Log Cleanup',
    jobType: 'cleanup-old-logs',
    schedule: '0 3 * * *',
    humanSchedule: 'Daily at 3 AM',
    description: 'Remove old log entries to free up space',
    priority: 2
  },
  {
    name: 'cleanup-expired-sessions',
    displayName: 'Session Cleanup',
    jobType: 'cleanup-expired-sessions',
    schedule: '0 */6 * * *',
    humanSchedule: 'Every 6 hours',
    description: 'Clean up expired user sessions',
    priority: 4
  },
  {
    name: 'weekly-report',
    displayName: 'Weekly Report',
    jobType: 'send-weekly-report',
    schedule: '0 9 * * 1',
    humanSchedule: 'Mondays at 9 AM',
    description: 'Send weekly system report to administrators',
    priority: 3
  }
]

// Computed
const hasRecommendations = computed(() => {
  return (props.config.maxConcurrentJobs > 10) ||
         (props.config.pollInterval < 3000) ||
         (props.config.debugMode && props.config.environment === 'production')
})

// Methods
const formatInterval = (ms) => {
  if (ms < 1000) return `${ms}ms`
  if (ms < 60000) return `${ms / 1000}s`
  return `${ms / 60000}m`
}

const getEnvironmentBadgeClass = (env) => {
  switch (env) {
    case 'production': return 'badge-success'
    case 'development': return 'badge-warning'
    case 'staging': return 'badge-info'
    default: return 'badge-ghost'
  }
}

const getPriorityBadgeClass = (priority) => {
  if (priority >= 7) return 'badge-error'
  if (priority >= 4) return 'badge-warning'
  return 'badge-ghost'
}
</script>
