<template>
  <div class="space-y-4">
    <!-- Filters -->
    <div class="card bg-base-100 shadow-sm border border-base-300">
      <div class="card-body p-4">
        <div class="flex flex-wrap gap-4 items-center">
          <div class="form-control">
            <label class="label">
              <span class="label-text">Status</span>
            </label>
            <select v-model="filters.status" class="select select-bordered select-sm">
              <option value="">All Statuses</option>
              <option value="pending">Pending</option>
              <option value="processing">Processing</option>
              <option value="completed">Completed</option>
              <option value="failed">Failed</option>
              <option value="retrying">Retrying</option>
            </select>
          </div>

          <div class="form-control">
            <label class="label">
              <span class="label-text">Job Type</span>
            </label>
            <select v-model="filters.jobType" class="select select-bordered select-sm">
              <option value="">All Types</option>
              <option value="calculate-daily-stats">Daily Stats</option>
              <option value="calculate-weekly-stats">Weekly Stats</option>
              <option value="calculate-monthly-stats">Monthly Stats</option>
              <option value="check-database-health">Database Health</option>
              <option value="check-memory-usage">Memory Check</option>
              <option value="cleanup-old-logs">Log Cleanup</option>
              <option value="cleanup-expired-sessions">Session Cleanup</option>
              <option value="send-daily-digest">Daily Digest</option>
            </select>
          </div>

          <div class="form-control">
            <label class="label">
              <span class="label-text">Priority</span>
            </label>
            <select v-model="filters.priority" class="select select-bordered select-sm">
              <option value="">All Priorities</option>
              <option value="high">High (7-10)</option>
              <option value="medium">Medium (4-6)</option>
              <option value="low">Low (0-3)</option>
            </select>
          </div>

          <div class="form-control">
            <label class="label">
              <span class="label-text">&nbsp;</span>
            </label>
            <button @click="applyFilters" class="btn btn-primary btn-sm">
              <Icon name="filter" size="sm" />
              Filter
            </button>
          </div>

          <div class="form-control">
            <label class="label">
              <span class="label-text">&nbsp;</span>
            </label>
            <button @click="clearFilters" class="btn btn-ghost btn-sm">
              Clear
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Job Statistics -->
    <div class="grid grid-cols-2 md:grid-cols-5 gap-4">
      <div class="stat bg-base-100 border border-base-300 rounded-lg">
        <div class="stat-title">Pending</div>
        <div class="stat-value text-warning">{{ jobStats.pending || 0 }}</div>
      </div>
      <div class="stat bg-base-100 border border-base-300 rounded-lg">
        <div class="stat-title">Processing</div>
        <div class="stat-value text-info">{{ jobStats.processing || 0 }}</div>
      </div>
      <div class="stat bg-base-100 border border-base-300 rounded-lg">
        <div class="stat-title">Completed</div>
        <div class="stat-value text-success">{{ jobStats.completed || 0 }}</div>
      </div>
      <div class="stat bg-base-100 border border-base-300 rounded-lg">
        <div class="stat-title">Failed</div>
        <div class="stat-value text-error">{{ jobStats.failed || 0 }}</div>
      </div>
      <div class="stat bg-base-100 border border-base-300 rounded-lg">
        <div class="stat-title">Total</div>
        <div class="stat-value">{{ jobStats.total || 0 }}</div>
      </div>
    </div>

    <!-- Jobs Table -->
    <div class="card bg-base-100 shadow-sm border border-base-300">
      <div class="card-body p-0">
        <div class="overflow-x-auto">
          <table class="table table-zebra">
            <thead>
              <tr>
                <th>ID</th>
                <th>Type</th>
                <th>Status</th>
                <th>Priority</th>
                <th>Attempts</th>
                <th>Created</th>
                <th>Duration</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr v-if="loading">
                <td colspan="8" class="text-center py-8">
                  <span class="loading loading-spinner loading-md"></span>
                  Loading jobs...
                </td>
              </tr>
              <tr v-else-if="filteredJobs.length === 0">
                <td colspan="8" class="text-center py-8 text-base-content/70">
                  No jobs found
                </td>
              </tr>
              <tr v-else v-for="job in filteredJobs" :key="job.id">
                <td class="font-mono">{{ job.id }}</td>
                <td>
                  <div class="flex flex-col">
                    <span class="font-medium">{{ formatJobType(job.job_type) }}</span>
                    <span class="text-xs text-base-content/70">{{ job.job_type }}</span>
                  </div>
                </td>
                <td>
                  <div class="badge" :class="getStatusBadgeClass(job.status)">
                    {{ job.status }}
                  </div>
                </td>
                <td>
                  <div class="badge" :class="getPriorityBadgeClass(job.priority)">
                    {{ job.priority }}
                  </div>
                </td>
                <td>
                  <span class="font-mono">{{ job.attempts }}/{{ job.max_attempts }}</span>
                </td>
                <td>
                  <div class="flex flex-col">
                    <span class="text-sm">{{ formatDate(job.created_at) }}</span>
                    <span class="text-xs text-base-content/70">{{ formatTime(job.created_at) }}</span>
                  </div>
                </td>
                <td>
                  <span v-if="job.completed_at && job.started_at" class="text-sm">
                    {{ calculateDuration(job.started_at, job.completed_at) }}
                  </span>
                  <span v-else-if="job.started_at" class="text-sm text-info">
                    {{ calculateDuration(job.started_at, new Date()) }} (running)
                  </span>
                  <span v-else class="text-xs text-base-content/70">-</span>
                </td>
                <td>
                  <div class="flex gap-1">
                    <button 
                      @click="viewJobDetails(job)" 
                      class="btn btn-ghost btn-xs"
                      title="View Details"
                    >
                      <Icon name="eye" size="xs" />
                    </button>
                    <button
                      v-if="job.status === 'pending' || job.status === 'retrying'"
                      @click="cancelJob(job.id)"
                      class="btn btn-ghost btn-xs text-error"
                      title="Cancel Job"
                    >
                      <Icon name="close" size="xs" />
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Job Details Modal -->
    <div v-if="selectedJob" class="modal modal-open">
      <div class="modal-box max-w-2xl">
        <h3 class="text-lg font-bold mb-4">Job Details</h3>
        
        <div class="space-y-4">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="label label-text font-semibold">Job ID</label>
              <div class="text-sm font-mono bg-base-200 p-2 rounded">{{ selectedJob.id }}</div>
            </div>
            <div>
              <label class="label label-text font-semibold">Status</label>
              <div class="badge" :class="getStatusBadgeClass(selectedJob.status)">
                {{ selectedJob.status }}
              </div>
            </div>
            <div>
              <label class="label label-text font-semibold">Type</label>
              <div class="text-sm bg-base-200 p-2 rounded">{{ selectedJob.job_type }}</div>
            </div>
            <div>
              <label class="label label-text font-semibold">Priority</label>
              <div class="badge" :class="getPriorityBadgeClass(selectedJob.priority)">
                {{ selectedJob.priority }}
              </div>
            </div>
          </div>

          <div>
            <label class="label label-text font-semibold">Job Data</label>
            <pre class="bg-base-200 p-3 rounded text-sm overflow-x-auto">{{ JSON.stringify(selectedJob.job_data, null, 2) }}</pre>
          </div>

          <div v-if="selectedJob.error_message">
            <label class="label label-text font-semibold">Error Message</label>
            <div class="bg-error/10 border border-error/20 p-3 rounded text-sm text-error">
              {{ selectedJob.error_message }}
            </div>
          </div>

          <div class="grid grid-cols-3 gap-4">
            <div>
              <label class="label label-text font-semibold">Created</label>
              <div class="text-sm bg-base-200 p-2 rounded">
                {{ formatDateTime(selectedJob.created_at) }}
              </div>
            </div>
            <div v-if="selectedJob.started_at">
              <label class="label label-text font-semibold">Started</label>
              <div class="text-sm bg-base-200 p-2 rounded">
                {{ formatDateTime(selectedJob.started_at) }}
              </div>
            </div>
            <div v-if="selectedJob.completed_at">
              <label class="label label-text font-semibold">Completed</label>
              <div class="text-sm bg-base-200 p-2 rounded">
                {{ formatDateTime(selectedJob.completed_at) }}
              </div>
            </div>
          </div>
        </div>

        <div class="modal-action">
          <button @click="selectedJob = null" class="btn">Close</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import Icon from '@/components/common/Icon.vue'

// Props
const props = defineProps({
  jobs: {
    type: Array,
    default: () => []
  },
  jobStats: {
    type: Object,
    default: () => ({})
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['refresh', 'cancel-job'])

// State
const selectedJob = ref(null)
const filters = ref({
  status: '',
  jobType: '',
  priority: ''
})

// Computed
const filteredJobs = computed(() => {
  let filtered = props.jobs

  if (filters.value.status) {
    filtered = filtered.filter(job => job.status === filters.value.status)
  }

  if (filters.value.jobType) {
    filtered = filtered.filter(job => job.job_type === filters.value.jobType)
  }

  if (filters.value.priority) {
    const priority = filters.value.priority
    filtered = filtered.filter(job => {
      if (priority === 'high') return job.priority >= 7
      if (priority === 'medium') return job.priority >= 4 && job.priority <= 6
      if (priority === 'low') return job.priority <= 3
      return true
    })
  }

  return filtered
})

// Methods
const formatJobType = (type) => {
  return type.split('-').map(word => 
    word.charAt(0).toUpperCase() + word.slice(1)
  ).join(' ')
}

const getStatusBadgeClass = (status) => {
  switch (status) {
    case 'pending': return 'badge-warning'
    case 'processing': return 'badge-info'
    case 'completed': return 'badge-success'
    case 'failed': return 'badge-error'
    case 'retrying': return 'badge-warning'
    default: return 'badge-ghost'
  }
}

const getPriorityBadgeClass = (priority) => {
  if (priority >= 7) return 'badge-error'
  if (priority >= 4) return 'badge-warning'
  return 'badge-ghost'
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString()
}

const formatTime = (dateString) => {
  return new Date(dateString).toLocaleTimeString()
}

const formatDateTime = (dateString) => {
  return new Date(dateString).toLocaleString()
}

const calculateDuration = (start, end) => {
  const duration = new Date(end) - new Date(start)
  const seconds = Math.floor(duration / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)

  if (hours > 0) {
    return `${hours}h ${minutes % 60}m`
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`
  } else {
    return `${seconds}s`
  }
}

const viewJobDetails = (job) => {
  selectedJob.value = job
}

const cancelJob = (jobId) => {
  if (confirm('Are you sure you want to cancel this job?')) {
    emit('cancel-job', jobId)
  }
}

const applyFilters = () => {
  emit('refresh')
}

const clearFilters = () => {
  filters.value = {
    status: '',
    jobType: '',
    priority: ''
  }
  emit('refresh')
}
</script>
