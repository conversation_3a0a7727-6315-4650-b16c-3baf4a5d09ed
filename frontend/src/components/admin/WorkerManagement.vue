<template>
  <div class="modal modal-open">
    <div class="modal-box max-w-7xl w-full h-full max-h-screen p-0 overflow-hidden">
      <!-- Modal Header -->
      <div class="sticky top-0 z-10 bg-base-100 border-b border-base-300 p-6">
        <div class="flex justify-between items-center">
          <div>
            <h2 class="text-2xl font-bold flex items-center gap-3">
              <Icon name="cog" size="lg" class="text-primary" />
              Worker Management
            </h2>
            <p class="text-base-content/70 mt-1">Monitor and manage background worker processes</p>
          </div>
          <button @click="$emit('close')" class="btn btn-ghost btn-circle">
            <Icon name="close" size="lg" />
          </button>
        </div>
      </div>

      <!-- Modal Content -->
      <div class="p-6 overflow-y-auto max-h-[calc(100vh-8rem)]">
        <div class="space-y-6">
    <!-- Action Buttons -->
    <div class="flex justify-end gap-2">
      <button @click="refreshData" class="btn btn-outline btn-sm" :disabled="isLoading">
        <Icon name="refresh" size="sm" :class="{ 'animate-spin': isLoading }" />
        Refresh
      </button>
      <button @click="showEnqueueModal = true" class="btn btn-primary btn-sm">
        <Icon name="plus" size="sm" />
        Enqueue Job
      </button>
    </div>

    <!-- Status Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <!-- Worker Status -->
      <div class="card bg-base-100 shadow-sm border border-base-300">
        <div class="card-body p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-base-content/70">Worker Status</p>
              <p class="text-lg font-semibold" :class="workerStatusColor">
                {{ workerStatus }}
              </p>
            </div>
            <div class="w-8 h-8 rounded-full flex items-center justify-center" :class="workerStatusBg">
              <Icon :name="workerStatusIcon" size="sm" class="text-white" />
            </div>
          </div>
        </div>
      </div>

      <!-- Job Queue -->
      <div class="card bg-base-100 shadow-sm border border-base-300">
        <div class="card-body p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-base-content/70">Pending Jobs</p>
              <p class="text-lg font-semibold">{{ jobStats.pending || 0 }}</p>
            </div>
            <div class="w-8 h-8 rounded-full bg-warning flex items-center justify-center">
              <Icon name="clock" size="sm" class="text-white" />
            </div>
          </div>
        </div>
      </div>

      <!-- Processing Jobs -->
      <div class="card bg-base-100 shadow-sm border border-base-300">
        <div class="card-body p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-base-content/70">Processing</p>
              <p class="text-lg font-semibold">{{ jobStats.processing || 0 }}</p>
            </div>
            <div class="w-8 h-8 rounded-full bg-info flex items-center justify-center">
              <Icon name="cog" size="sm" class="text-white animate-spin" />
            </div>
          </div>
        </div>
      </div>

      <!-- System Health -->
      <div class="card bg-base-100 shadow-sm border border-base-300">
        <div class="card-body p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-base-content/70">System Health</p>
              <p class="text-lg font-semibold" :class="healthStatusColor">
                {{ healthStatus }}
              </p>
            </div>
            <div class="w-8 h-8 rounded-full flex items-center justify-center" :class="healthStatusBg">
              <Icon :name="healthStatusIcon" size="sm" class="text-white" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Tabs -->
    <div class="tabs tabs-bordered">
      <button 
        v-for="tab in tabs" 
        :key="tab.id"
        @click="activeTab = tab.id"
        :class="['tab', { 'tab-active': activeTab === tab.id }]"
      >
        <Icon :name="tab.icon" size="sm" class="mr-2" />
        {{ tab.name }}
      </button>
    </div>

    <!-- Tab Content -->
    <div class="min-h-96">
      <!-- Job Queue Tab -->
      <div v-if="activeTab === 'jobs'" class="space-y-4">
        <JobQueueManager 
          :jobs="jobs" 
          :loading="isLoading"
          @refresh="fetchJobs"
          @cancel-job="cancelJob"
        />
      </div>

      <!-- Statistics Tab -->
      <div v-if="activeTab === 'stats'" class="space-y-4">
        <SystemStatistics 
          :stats="systemStats" 
          :loading="isLoading"
          @refresh="fetchStats"
        />
      </div>

      <!-- Health Tab -->
      <div v-if="activeTab === 'health'" class="space-y-4">
        <HealthMonitoring 
          :health="healthData" 
          :loading="isLoading"
          @refresh="fetchHealth"
          @run-checks="runHealthChecks"
        />
      </div>

      <!-- Configuration Tab -->
      <div v-if="activeTab === 'config'" class="space-y-4">
        <WorkerConfiguration 
          :config="workerConfig" 
          :loading="isLoading"
          @refresh="fetchConfig"
        />
      </div>

      <!-- Logs Tab -->
      <div v-if="activeTab === 'logs'" class="space-y-4">
        <WorkerLogs 
          :logs="workerLogs" 
          :loading="isLoading"
          @refresh="fetchLogs"
        />
      </div>
    </div>

        <!-- Enqueue Job Modal -->
        <EnqueueJobModal
          v-if="showEnqueueModal"
          @close="showEnqueueModal = false"
          @job-enqueued="handleJobEnqueued"
        />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { apiService } from '@/services/api'
import Icon from '@/components/common/Icon.vue'
import JobQueueManager from './worker/JobQueueManager.vue'
import SystemStatistics from './worker/SystemStatistics.vue'
import HealthMonitoring from './worker/HealthMonitoring.vue'
import WorkerConfiguration from './worker/WorkerConfiguration.vue'
import WorkerLogs from './worker/WorkerLogs.vue'
import EnqueueJobModal from './worker/EnqueueJobModal.vue'

// Emits
const emit = defineEmits(['close'])

// Auth
const authStore = useAuthStore()

// State
const isLoading = ref(false)
const activeTab = ref('jobs')
const showEnqueueModal = ref(false)

// Data
const jobs = ref([])
const jobStats = ref({})
const systemStats = ref({})
const healthData = ref({})
const workerConfig = ref({})
const workerLogs = ref([])

// Tabs configuration
const tabs = [
  { id: 'jobs', name: 'Job Queue', icon: 'list' },
  { id: 'stats', name: 'Statistics', icon: 'chart-bar' },
  { id: 'health', name: 'Health', icon: 'heart' },
  { id: 'config', name: 'Configuration', icon: 'cog' },
  { id: 'logs', name: 'Logs', icon: 'document-text' },
]

// Computed
const workerStatus = computed(() => {
  if (healthData.value?.status === 'healthy') return 'Running'
  if (healthData.value?.status === 'degraded') return 'Degraded'
  if (healthData.value?.status === 'unhealthy') return 'Unhealthy'
  return 'Unknown'
})

const workerStatusColor = computed(() => {
  const status = workerStatus.value
  if (status === 'Running') return 'text-success'
  if (status === 'Degraded') return 'text-warning'
  if (status === 'Unhealthy') return 'text-error'
  return 'text-base-content/70'
})

const workerStatusBg = computed(() => {
  const status = workerStatus.value
  if (status === 'Running') return 'bg-success'
  if (status === 'Degraded') return 'bg-warning'
  if (status === 'Unhealthy') return 'bg-error'
  return 'bg-base-content/20'
})

const workerStatusIcon = computed(() => {
  const status = workerStatus.value
  if (status === 'Running') return 'check'
  if (status === 'Degraded') return 'exclamation-triangle'
  if (status === 'Unhealthy') return 'close'
  return 'question'
})

const healthStatus = computed(() => {
  return healthData.value?.status || 'Unknown'
})

const healthStatusColor = computed(() => {
  const status = healthStatus.value
  if (status === 'healthy') return 'text-success'
  if (status === 'degraded') return 'text-warning'
  if (status === 'unhealthy') return 'text-error'
  return 'text-base-content/70'
})

const healthStatusBg = computed(() => {
  const status = healthStatus.value
  if (status === 'healthy') return 'bg-success'
  if (status === 'degraded') return 'bg-warning'
  if (status === 'unhealthy') return 'bg-error'
  return 'bg-base-content/20'
})

const healthStatusIcon = computed(() => {
  const status = healthStatus.value
  if (status === 'healthy') return 'heart'
  if (status === 'degraded') return 'exclamation-triangle'
  if (status === 'unhealthy') return 'close'
  return 'question'
})

// Methods
const fetchJobs = async () => {
  try {
    const response = await apiService.get('/worker/jobs')
    if (response.success) {
      jobs.value = response.data.jobs || []
      jobStats.value = response.data.stats || {}
    }
  } catch (error) {
    console.error('Error fetching jobs:', error)
  }
}

const fetchStats = async () => {
  try {
    const response = await apiService.get('/worker/stats/summary')
    if (response.success) {
      systemStats.value = response.data
    }
  } catch (error) {
    console.error('Error fetching stats:', error)
  }
}

const fetchHealth = async () => {
  try {
    const response = await apiService.get('/worker/health')
    if (response.success) {
      healthData.value = response.data
    }
  } catch (error) {
    console.error('Error fetching health:', error)
  }
}

const fetchConfig = async () => {
  try {
    const response = await apiService.get('/worker/config')
    if (response.success) {
      workerConfig.value = response.data
    }
  } catch (error) {
    console.error('Error fetching config:', error)
  }
}

const fetchLogs = async () => {
  try {
    const response = await apiService.get('/worker/logs', { params: { limit: 100 } })
    if (response.success) {
      workerLogs.value = response.data.logs || []
    }
  } catch (error) {
    console.error('Error fetching logs:', error)
  }
}

const refreshData = async () => {
  isLoading.value = true
  try {
    await Promise.all([
      fetchJobs(),
      fetchStats(),
      fetchHealth(),
      fetchConfig(),
      fetchLogs(),
    ])
  } finally {
    isLoading.value = false
  }
}

const cancelJob = async (jobId) => {
  try {
    await apiService.delete(`/worker/jobs/${jobId}`)
    await fetchJobs() // Refresh jobs list
  } catch (error) {
    console.error('Error cancelling job:', error)
  }
}

const runHealthChecks = async () => {
  try {
    await apiService.post('/worker/health/check')
    await fetchHealth() // Refresh health data
  } catch (error) {
    console.error('Error running health checks:', error)
  }
}

const handleJobEnqueued = () => {
  showEnqueueModal.value = false
  fetchJobs() // Refresh jobs list
}

// Auto-refresh
let refreshInterval = null

onMounted(() => {
  refreshData()
  
  // Auto-refresh every 30 seconds
  refreshInterval = setInterval(() => {
    if (activeTab.value === 'jobs') {
      fetchJobs()
    } else if (activeTab.value === 'health') {
      fetchHealth()
    }
  }, 30000)
})

onUnmounted(() => {
  if (refreshInterval) {
    clearInterval(refreshInterval)
  }
})
</script>
