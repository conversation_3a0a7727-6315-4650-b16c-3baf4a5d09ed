<template>
  <section class="hero min-h-screen bg-gradient-to-br from-primary via-primary-focus to-secondary relative overflow-hidden">
    <!-- Hero Content -->
    <div class="hero-content text-center text-primary-content relative z-10 px-4 sm:px-6">
      <div class="max-w-4xl w-full">
        <!-- Logo and Main Heading -->
        <div class="flex flex-col items-center mb-6 sm:mb-8">
          <div class="mb-4 sm:mb-6">
            <!-- Company logo for fastest LCP -->
            <div class="w-16 h-16 sm:w-20 sm:h-20 mx-auto animate-fade-in">
              <img
                src="/hl-energy-logo-256w.png"
                alt="HL Energy Logo"
                class="w-full h-full object-contain"
                loading="eager"
              />
            </div>
          </div>
          <h1 class="text-3xl sm:text-4xl md:text-6xl font-bold leading-tight text-center">
            {{ t('hero.title') }}
          </h1>
        </div>

        <!-- Subtitle -->
        <p class="mb-6 sm:mb-8 text-base sm:text-lg md:text-xl max-w-3xl mx-auto leading-relaxed opacity-90 px-2">
          {{ t('hero.subtitle') }}
        </p>

        <!-- Company Introduction -->
        <div class="mb-6 sm:mb-8">
          <div class="inline-flex items-center bg-white/20 backdrop-blur-sm rounded-full px-4 py-2 mb-4">
            <Icon name="building" size="sm" class="mr-2 text-white" />
            <span class="text-sm font-medium text-white">{{ t('hero.professional_consultation') }}</span>
          </div>
          <div class="flex flex-wrap justify-center gap-2 mb-4">
            <span class="badge badge-lg bg-accent/70 text-accent-content border-0">{{ t('hero.badges.b2b_solutions') }}</span>
            <span class="badge badge-lg bg-secondary/70 text-secondary-content border-0">{{ t('hero.badges.b2c_services') }}</span>
            <span class="badge badge-lg bg-success/70 text-success-content border-0">{{ t('hero.badges.experience') }}</span>
          </div>
        </div>

        <!-- CTA Buttons -->
        <div class="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center mb-8 sm:mb-12 px-2">
          <RouterLink
            to="/contact"
            class="btn btn-accent btn-contact btn-touch"
          >
            <Icon name="email" size="sm" class="btn-icon" />
            <span class="contact-text hidden sm:inline">{{ t('hero.cta') }}</span>
            <span class="contact-text sm:hidden">{{ t('hero.cta_short') }}</span>
            <Icon name="arrow-right" size="sm" class="btn-icon" />
          </RouterLink>
          <!-- <RouterLink
            to="/services"
            class="btn btn-outline btn-secondary btn-contact btn-touch border-2 hover:bg-secondary hover:text-secondary-content"
          >
            <Icon name="lightbulb" size="sm" class="btn-icon" />
            <span class="contact-text hidden sm:inline">{{ t('hero.learn_more') }}</span>
            <span class="contact-text sm:hidden">{{ t('hero.learn_more_short') }}</span>
          </RouterLink> -->
        </div>

        <!-- Stats Section -->
        <div class="grid grid-cols-1 sm:grid-cols-3 gap-6 sm:gap-8 max-w-3xl mx-auto px-4">
          <div class="text-center">
            <div class="text-2xl sm:text-3xl md:text-4xl font-bold mb-2">{{ t('hero.stats.clients') }}</div>
            <div class="text-xs sm:text-sm md:text-base opacity-80">{{ t('hero.stats.clients_desc') }}</div>
          </div>
          <div class="text-center">
            <div class="text-2xl sm:text-3xl md:text-4xl font-bold mb-2">{{ t('hero.stats.savings') }}</div>
            <div class="text-xs sm:text-sm md:text-base opacity-80">{{ t('hero.stats.savings_desc') }}</div>
          </div>
          <div class="text-center">
            <div class="text-2xl sm:text-3xl md:text-4xl font-bold mb-2">{{ t('hero.stats.experience') }}</div>
            <div class="text-xs sm:text-sm md:text-base opacity-80">{{ t('hero.stats.experience_desc') }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Scroll Indicator -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
      <div class="w-6 h-6 text-white opacity-70">↓</div>
    </div>
  </section>
</template>

<script setup lang="ts">
// PWA functionality removed for faster LCP
import { useI18n } from 'vue-i18n'
import Icon from '@/components/common/Icon.vue'
// Logo is now inline SVG for faster LCP

const { t } = useI18n()
</script>

<style scoped>
.hero {
  background-attachment: fixed;
}
</style>
