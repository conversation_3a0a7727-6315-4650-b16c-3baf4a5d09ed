<template>
  <section class="py-20 bg-gradient-to-br from-primary via-primary-focus to-secondary relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-10">
      <div class="absolute inset-0 bg-gradient-to-br from-white/5 to-white/10"></div>
    </div>

    <div class="max-w-7xl mx-auto px-6 lg:px-8 relative z-10">
      <!-- Section Header -->
      <div class="text-center mb-16">
        <h2 class="text-4xl md:text-5xl font-bold mb-6 text-primary-content">{{ t('cta_section.title') }}</h2>
        <p class="text-xl text-primary-content/90 max-w-3xl mx-auto mb-8 leading-relaxed">{{ t('cta_section.subtitle') }}</p>
      </div>

      <!-- CTA Options -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 mb-16">
        <!-- Business CTA -->
        <div class="group">
          <div class="card bg-base-100/95 backdrop-blur-sm shadow-2xl border border-base-300/50 hover:shadow-3xl transition-all duration-500 transform hover:scale-105">
            <div class="card-body p-8 lg:p-10 text-center">
              <!-- Icon -->
              <div class="flex justify-center mb-6">
                <div class="p-5 bg-gradient-to-br from-primary/30 to-primary/10 rounded-3xl shadow-xl border border-primary/20 group-hover:shadow-2xl group-hover:scale-110 transition-all duration-300">
                  <Icon name="building" size="2xl" class="text-primary drop-shadow-sm" />
                </div>
              </div>

              <!-- Title -->
              <h3 class="text-2xl font-bold mb-4 text-base-content">{{ t('cta_section.business_cta') }}</h3>
              
              <!-- Description -->
              <p class="text-base-content/70 mb-6 leading-relaxed">
                {{ t('cta_section.business_desc') }}
              </p>

              <!-- Features -->
              <div class="space-y-3 mb-8">
                <div class="flex items-center justify-center text-sm text-base-content/80">
                  <Icon name="check" size="sm" class="text-success mr-2" />
                  {{ t('cta_section.business_feature_1') }}
                </div>
                <div class="flex items-center justify-center text-sm text-base-content/80">
                  <Icon name="check" size="sm" class="text-success mr-2" />
                  {{ t('cta_section.business_feature_2') }}
                </div>
                <div class="flex items-center justify-center text-sm text-base-content/80">
                  <Icon name="check" size="sm" class="text-success mr-2" />
                  {{ t('cta_section.business_feature_3') }}
                </div>
                <div class="flex items-center justify-center text-sm text-base-content/80">
                  <Icon name="check" size="sm" class="text-success mr-2" />
                  {{ t('cta_section.business_feature_4') }}
                </div>
              </div>

              <!-- CTA Button -->
              <RouterLink
                to="/contact"
                class="btn btn-primary btn-cta-lg w-full"
              >
                <Icon name="email" size="sm" class="btn-icon" />
                <span class="btn-text hidden sm:inline">{{ t('cta_section.business_cta') }}</span>
                <span class="btn-text sm:hidden">{{ t('cta_section.business_cta_short') }}</span>
                <Icon name="arrow-right" size="sm" class="btn-icon" />
              </RouterLink>
            </div>
          </div>
        </div>

        <!-- Residential CTA -->
        <div class="group">
          <div class="card bg-base-100/95 backdrop-blur-sm shadow-2xl border border-base-300/50 hover:shadow-3xl transition-all duration-500 transform hover:scale-105">
            <div class="card-body p-8 lg:p-10 text-center">
              <!-- Icon -->
              <div class="flex justify-center mb-6">
                <div class="p-5 bg-gradient-to-br from-secondary/30 to-secondary/10 rounded-3xl shadow-xl border border-secondary/20 group-hover:shadow-2xl group-hover:scale-110 transition-all duration-300">
                  <Icon name="home" size="2xl" class="text-secondary drop-shadow-sm" />
                </div>
              </div>

              <!-- Title -->
              <h3 class="text-2xl font-bold mb-4 text-base-content">{{ t('cta_section.residential_cta') }}</h3>
              
              <!-- Description -->
              <p class="text-base-content/70 mb-6 leading-relaxed">
                {{ t('cta_section.residential_desc') }}
              </p>

              <!-- Features -->
              <div class="space-y-3 mb-8">
                <div class="flex items-center justify-center text-sm text-base-content/80">
                  <Icon name="check" size="sm" class="text-success mr-2" />
                  {{ t('cta_section.residential_feature_1') }}
                </div>
                <div class="flex items-center justify-center text-sm text-base-content/80">
                  <Icon name="check" size="sm" class="text-success mr-2" />
                  {{ t('cta_section.residential_feature_2') }}
                </div>
                <div class="flex items-center justify-center text-sm text-base-content/80">
                  <Icon name="check" size="sm" class="text-success mr-2" />
                  {{ t('cta_section.residential_feature_3') }}
                </div>
                <div class="flex items-center justify-center text-sm text-base-content/80">
                  <Icon name="check" size="sm" class="text-success mr-2" />
                  {{ t('cta_section.residential_feature_4') }}
                </div>
              </div>

              <!-- CTA Button -->
              <RouterLink
                to="/contact"
                class="btn btn-secondary btn-cta-lg w-full"
              >
                <Icon name="home" size="sm" class="btn-icon" />
                <span class="btn-text hidden sm:inline">{{ t('cta_section.residential_cta') }}</span>
                <span class="btn-text sm:hidden">{{ t('cta_section.residential_cta_short') }}</span>
                <Icon name="arrow-right" size="sm" class="btn-icon" />
              </RouterLink>
            </div>
          </div>
        </div>
      </div>

      <!-- Free Assessment CTA -->
      <!-- <div class="text-center">
        <div class="inline-block p-1 bg-gradient-to-r from-accent to-warning rounded-2xl shadow-2xl">
          <div class="bg-base-100 rounded-xl p-8 lg:p-10">
            <h3 class="text-2xl lg:text-3xl font-bold mb-4 text-base-content">{{ t('cta_section.free_assessment') }}</h3>
            <p class="text-base-content/70 mb-6 max-w-2xl mx-auto">
              Not sure which solution is right for you? Start with our complimentary energy assessment and discover your savings potential.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
              <RouterLink
                to="/contact"
                class="btn btn-accent btn-assessment"
              >
                <Icon name="lightbulb" size="sm" class="btn-icon" />
                <span class="assessment-text hidden sm:inline">{{ t('cta_section.contact_cta') }}</span>
                <span class="assessment-text sm:hidden">{{ t('cta_section.contact_cta_short') }}</span>
              </RouterLink>
              <a
                href="tel:******-ENERGY"
                class="btn btn-outline btn-lg px-4 sm:px-8 py-3 sm:py-4 text-sm sm:text-lg font-semibold border-2 hover:bg-accent hover:text-accent-content transition-all duration-300 min-h-[3rem] sm:min-h-[3.5rem] flex-nowrap"
              >
                <Icon name="phone" size="sm" class="mr-1 sm:mr-2 flex-shrink-0" />
                <span class="truncate hidden sm:inline">Call Now: (555) ENERGY</span>
                <span class="truncate sm:hidden">Call Now</span>
              </a>
            </div>
          </div>
        </div>
      </div> -->

      <!-- Trust Badges -->
      <!-- <div class="mt-16 text-center">
        <div class="flex flex-wrap justify-center items-center gap-8 opacity-80">
          <div class="flex items-center text-primary-content/70">
            <Icon name="shield" size="sm" class="mr-2" />
            <span class="text-sm font-medium">ISO 50001 Certified</span>
          </div>
          <div class="flex items-center text-primary-content/70">
            <Icon name="star" size="sm" class="mr-2" />
            <span class="text-sm font-medium">15+ Years Experience</span>
          </div>
          <div class="flex items-center text-primary-content/70">
            <Icon name="users" size="sm" class="mr-2" />
            <span class="text-sm font-medium">500+ Satisfied Clients</span>
          </div>
          <div class="flex items-center text-primary-content/70">
            <Icon name="check-circle" size="sm" class="mr-2" />
            <span class="text-sm font-medium">100% Satisfaction Guarantee</span>
          </div>
        </div>
      </div> -->
    </div>
  </section>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import Icon from '@/components/common/Icon.vue'

const { t } = useI18n()
</script>

<style scoped>
/* Enhanced shadow effects */
.shadow-3xl {
  box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
}

/* Smooth backdrop blur */
.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

/* Enhanced hover animations */
.group:hover .card {
  transform: translateY(-4px) scale(1.02);
}

/* Smooth transitions for all interactive elements */
* {
  transition-property: transform, box-shadow, background-color, border-color, opacity, color;
  transition-duration: 300ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
</style>
