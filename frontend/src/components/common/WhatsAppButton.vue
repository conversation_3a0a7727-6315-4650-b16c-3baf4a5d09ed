<template>
  <div
    class="fixed z-50 transition-all duration-300"
    :class="buttonPositionClasses"
  >
    <a
      :href="whatsappUrl"
      target="_blank"
      rel="noopener noreferrer"
      class="btn btn-circle btn-lg bg-green-500 hover:bg-green-600 border-0 shadow-2xl hover:shadow-3xl transition-all duration-300 transform hover:scale-110 group relative"
      :title="t('whatsapp.contact_us')"
    >
      <!-- WhatsApp Icon -->
      <svg
        class="w-8 h-8 text-white group-hover:scale-110 transition-transform duration-200"
        fill="currentColor"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.525 3.488"/>
      </svg>
      
      <!-- Pulse animation -->
      <div class="absolute inset-0 rounded-full bg-green-400 animate-ping opacity-20"></div>
    </a>
    
    <!-- Tooltip -->
    <div
      class="absolute bottom-full mb-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none"
      :class="tooltipPositionClasses"
    >
      <div class="bg-gray-900 text-white text-sm px-3 py-2 rounded-lg whitespace-nowrap shadow-lg">
        {{ t('whatsapp.contact_us') }}
        <div
          class="absolute top-full w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"
          :class="tooltipArrowClasses"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

interface Props {
  phoneNumber?: string
  message?: string
  isContactPage?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  phoneNumber: '+351938235730', // Default Portuguese number
  message: '',
  isContactPage: false
})

const { t } = useI18n()

const whatsappUrl = computed(() => {
  const defaultMessage = t('whatsapp.default_message')
  const message = props.message || defaultMessage
  const encodedMessage = encodeURIComponent(message)
  return `https://wa.me/${props.phoneNumber.replace(/[^0-9]/g, '')}?text=${encodedMessage}`
})

// Dynamic positioning based on page and screen size
const buttonPositionClasses = computed(() => {
  if (props.isContactPage) {
    // On contact page, position on the left to avoid reCAPTCHA
    return 'bottom-6 left-6 sm:left-6 md:left-8'
  } else {
    // On other pages, position on the right as usual
    return 'bottom-6 right-6 sm:right-6 md:right-8'
  }
})

// Dynamic tooltip positioning based on button position
const tooltipPositionClasses = computed(() => {
  if (props.isContactPage) {
    // When button is on the left, show tooltip on the right side
    return 'left-0'
  } else {
    // When button is on the right, show tooltip on the right side (default)
    return 'right-0'
  }
})

// Dynamic tooltip arrow positioning
const tooltipArrowClasses = computed(() => {
  if (props.isContactPage) {
    // When button is on the left, position arrow on the left side of tooltip
    return 'left-4'
  } else {
    // When button is on the right, position arrow on the right side of tooltip
    return 'right-4'
  }
})
</script>

<style scoped>
/* Enhanced shadow effects */
.shadow-3xl {
  box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
}

/* Smooth animations */
.btn-circle {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  /* Ensure perfect circle */
  aspect-ratio: 1;
  border-radius: 50% !important;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

/* Ensure pulse animation is perfectly aligned */
.btn-circle .animate-ping {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
}

/* Pulse animation for attention */
@keyframes pulse-green {
  0%, 100% {
    opacity: 0.2;
    transform: scale(1);
  }
  50% {
    opacity: 0.1;
    transform: scale(1.1);
  }
}

.animate-ping {
  animation: pulse-green 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Hover effects */
.group:hover .animate-ping {
  animation-duration: 1s;
}

/* Mobile responsiveness - handled by dynamic classes now */
@media (max-width: 640px) {
  /* Ensure button is properly sized on mobile */
  .btn-circle {
    width: 3.5rem !important;
    height: 3.5rem !important;
    min-width: 3.5rem;
    min-height: 3.5rem;
  }

  /* Ensure icon scales properly on mobile */
  .btn-circle svg {
    width: 1.75rem;
    height: 1.75rem;
  }
}
</style>
