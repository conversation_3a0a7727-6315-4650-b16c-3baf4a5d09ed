import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue' // Keep home view for fast initial load
import { useAuthStore } from '@/stores/auth'
import { autoCleanupOnNavigation } from '@/utils/recaptchaCleanup'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
    },
    // {
    //   path: '/about',
    //   name: 'about',
    //   component: () => import('../views/AboutView.vue'),
    // },
    // {
    //   path: '/services',
    //   name: 'services',
    //   component: () => import('../views/ServicesView.vue'),
    // },
    {
      path: '/contact',
      name: 'contact',
      component: () => import('../views/ContactView.vue'),
    },
    {
      path: '/privacy-policy',
      name: 'privacy-policy',
      component: () => import('../views/PrivacyPolicy.vue'),
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/LoginView.vue'),
    },
    {
      path: '/register',
      name: 'register',
      component: () => import('../views/RegisterView.vue'),
    },
    {
      path: '/dashboard',
      name: 'dashboard',
      component: () => import('../views/DashboardView.vue'),
      meta: { requiresAuth: true, hideHeader: true, hideFooter: true }
    },
    {
      path: '/profile',
      name: 'profile',
      component: () => import('../views/ProfileView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/admin/login',
      name: 'admin-login',
      component: () => import('../components/admin/AdminLogin.vue'),
      meta: { title: 'Admin Login', hideNavigation: true }
    },
    {
      path: '/admin',
      name: 'admin',
      component: () => import('../views/AdminView.vue'),
      meta: { requiresAuth: true, requiresAdmin: true, hideHeader: true, hideFooter: true }
    },
    // Theme demo route removed for cleaner production builds
    // Socket test route removed for cleaner production builds
    {
      path: '/admin/socket-monitor',
      name: 'socket-monitor',
      component: () => import('../components/admin/SocketMonitor.vue'),
      meta: { requiresAuth: true, requiresAdmin: true }
    },
    {
      path: '/admin/logs',
      name: 'admin-logs',
      component: () => import('../components/admin/SystemLogs.vue'),
      meta: { requiresAuth: true, requiresAdmin: true, title: 'System Logs' }
    },
    {
      path: '/admin/seo',
      name: 'admin-seo',
      component: () => import('../views/SEOView.vue'),
      meta: { requiresAuth: true, requiresAdmin: true, title: 'SEO Management' }
    },
    {
      path: '/admin/leads',
      name: 'admin-leads',
      component: () => import('../views/LeadsView.vue'),
      meta: { requiresAuth: true, requiresAdmin: true, title: 'Lead Management' }
    },
    {
      path: '/admin/worker',
      name: 'admin-worker',
      component: () => import('../components/admin/WorkerManagement.vue'),
      meta: { requiresAuth: true, requiresAdmin: true, title: 'Worker Management' }
    },
    // Debug login route removed for cleaner production builds

    {
      path: '/crm',
      name: 'crm',
      redirect: '/crm/dashboard',
      meta: { requiresAuth: true },
      children: [
        {
          path: 'dashboard',
          name: 'crm-dashboard',
          component: () => import('../components/crm/CRMDashboard.vue')
        },
        {
          path: 'customers',
          name: 'crm-customers',
          component: () => import('../components/crm/CustomerList.vue')
        },
        {
          path: 'customers/:id',
          name: 'crm-customer-detail',
          component: () => import('../components/crm/CustomerDetail.vue')
        },
        {
          path: 'projects',
          name: 'crm-projects',
          component: () => import('../components/crm/ProjectList.vue')
        },
        {
          path: 'communications',
          name: 'crm-communications',
          component: () => import('../components/crm/CommunicationHub.vue')
        }
      ]
    },
    // 404 Catch-all route - must be last
    {
      path: '/:pathMatch(.*)*',
      name: 'not-found',
      component: () => import('../views/NotFoundView.vue'),
      meta: {
        title: 'Page Not Found',
        hideFromSitemap: true
      }
    }
  ],
})

// Navigation guards
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  // Clean up reCAPTCHA when navigating away from contact page
  if (from.name === 'contact' && to.name !== 'contact') {
    autoCleanupOnNavigation()
  }

  // Wait for auth initialization if we have a token but no user data
  if (authStore.token && !authStore.user) {
    try {
      await authStore.initializeAuth()
    } catch (error) {
      console.warn('Auth initialization failed:', error)
      // Continue with navigation, auth state will be cleared
    }
  }

  // Define login/auth pages that authenticated users shouldn't access
  const authPages = ['login', 'register', 'admin-login']

  // SAFETY CHECK: If we have inconsistent auth state, clear it
  if (authStore.token && !authStore.user && !authStore.isLocked) {
    console.warn('🚨 Inconsistent auth state detected - logging out')
    await authStore.logout()
  }

  // If user is authenticated and trying to access login/register pages
  if (authStore.isAuthenticated && authPages.includes(to.name as string)) {
    // Check if there's a redirect query parameter from the original request
    const redirectPath = to.query.redirect as string

    if (redirectPath) {
      // Redirect to the originally requested page
      next(redirectPath)
    } else {
      // Redirect based on user role
      if (authStore.isAdmin && to.name === 'admin-login') {
        next({ name: 'admin' })
      } else if (to.name === 'admin-login') {
        // Non-admin trying to access admin login, redirect to regular dashboard
        next({ name: 'dashboard' })
      } else {
        // Regular login/register, redirect to dashboard
        next({ name: 'dashboard' })
      }
    }
    return
  }

  // Check if route requires authentication
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    console.log('🔒 Route requires auth but user not authenticated, redirecting to login')
    // Redirect to appropriate login page
    if (to.meta.requiresAdmin) {
      next({ name: 'admin-login', query: { redirect: to.fullPath } })
    } else {
      next({ name: 'login', query: { redirect: to.fullPath } })
    }
  } else if (to.meta.requiresAdmin && !authStore.isAdmin) {
    console.log('🔒 Route requires admin but user not admin, redirecting to admin login')
    // Redirect to admin login if not admin
    next({ name: 'admin-login', query: { redirect: to.fullPath } })
  } else {
    console.log('✅ Navigation allowed to:', to.name)
    // Allow navigation - if session is locked, the SessionLock modal will handle the UI
    // No redirects needed for locked sessions, user stays on the requested page
    next()
  }
})

// Handle navigation errors (especially for offline scenarios and chunk loading failures)
router.onError((error) => {
  console.error('Router navigation error:', error)

  // Check if it's a dynamic import error or chunk loading error
  if (error.message && (
    error.message.includes('Failed to fetch dynamically imported module') ||
    error.message.includes('Loading chunk') ||
    error.message.includes('Loading CSS chunk')
  )) {
    console.log('🔄 Dynamic import/chunk loading failed')

    // Check if we're offline
    if (!navigator.onLine) {
      console.log('User is offline - redirecting to cached pages')

      // Redirect to offline page or cached page
      const cachedPages = ['/', '/about', '/services', '/contact', '/dashboard']
      const currentPath = window.location.pathname

      if (cachedPages.includes(currentPath)) {
        // Try to reload the current page
        window.location.reload()
      } else {
        // Redirect to home page which should be cached
        router.replace('/')
      }
    } else {
      // User is online but chunk loading failed (likely due to new deployment)
      console.log('🔄 User is online but chunk loading failed - likely new deployment, reloading page')

      // Clear caches and reload to get the new version
      if ('caches' in window) {
        caches.keys().then(cacheNames => {
          cacheNames.forEach(cacheName => {
            caches.delete(cacheName)
          })
        }).finally(() => {
          window.location.reload()
        })
      } else {
        window.location.reload()
      }
    }
  }
})

export default router
