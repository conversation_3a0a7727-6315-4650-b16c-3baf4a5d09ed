<script setup lang="ts">
import { defineAsyncComponent } from 'vue'

// Only load hero immediately for LCP
import HeroSection from '@/components/features/HeroSection.vue'

// Lazy load everything else
// const AboutSection = defineAsyncComponent(() => import('@/components/features/AboutSection.vue'))
// const ServicesSection = defineAsyncComponent(() => import('@/components/features/ServicesSection.vue'))
// const TestimonialsSection = defineAsyncComponent(() => import('@/components/features/TestimonialsSection.vue'))
// const CTASection = defineAsyncComponent(() => import('@/components/features/CTASection.vue'))
const PWAInstallBanner = defineAsyncComponent(() => import('@/components/PWAInstallBanner.vue'))
</script>

<template>
  <div>
    <!-- Load PWA banner and hero immediately for LCP -->
    <PWAInstallBanner />
    <HeroSection />

    <!-- Load other sections progressively -->
    <!-- <Suspense>
      <template #default>
        <div>
          <AboutSection />
          <ServicesSection />
          <TestimonialsSection />
          <CTASection />
        </div>
      </template>
      <template #fallback>
        <div class="min-h-screen flex items-center justify-center">
          <div class="loading loading-spinner loading-lg"></div>
        </div>
      </template>
    </Suspense> -->
  </div>
</template>
