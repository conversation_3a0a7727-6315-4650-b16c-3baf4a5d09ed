<template>
  <div class="offline-view min-h-screen bg-base-200 flex items-center justify-center p-4">
    <div class="card bg-base-100 shadow-xl max-w-md w-full">
      <div class="card-body text-center">
        <!-- Offline Icon -->
        <div class="mb-6">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-20 w-20 mx-auto text-warning" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2v6m0 8v6m8-10h-6m-8 0h6" />
          </svg>
        </div>

        <!-- Title -->
        <h1 class="card-title text-2xl mb-4 justify-center">
          You're Offline
        </h1>

        <!-- Description -->
        <p class="text-base-content opacity-70 mb-6">
          It looks like you're not connected to the internet. Don't worry! You can still access cached pages and use offline features.
        </p>

        <!-- Network Status -->
        <div class="alert alert-warning mb-6">
          <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
          <div>
            <h3 class="font-bold">No Internet Connection</h3>
            <div class="text-xs">{{ networkStatus }}</div>
          </div>
        </div>

        <!-- Available Actions -->
        <div class="space-y-3 mb-6">
          <h3 class="font-semibold text-lg">Available Offline:</h3>
          
          <div class="grid grid-cols-2 gap-3">
            <RouterLink to="/" class="btn btn-primary btn-sm">
              🏠 Home
            </RouterLink>
            <!-- <RouterLink to="/about" class="btn btn-primary btn-sm">
              ℹ️ About
            </RouterLink> -->
            <!-- <RouterLink to="/services" class="btn btn-primary btn-sm">
              ⚡ Services
            </RouterLink> -->
            <RouterLink to="/contact" class="btn btn-primary btn-sm">
              📞 Contact
            </RouterLink>
          </div>

          <RouterLink to="/dashboard" class="btn btn-secondary btn-sm w-full" v-if="isAuthenticated">
            📊 Dashboard
          </RouterLink>
        </div>

        <!-- Cache Information -->
        <div class="bg-base-200 p-4 rounded-lg mb-6">
          <h4 class="font-semibold mb-2">📦 Cached Content</h4>
          <div class="text-sm space-y-1">
            <div class="flex justify-between">
              <span>Cached Pages:</span>
              <span class="font-medium">{{ cacheStats.totalPages }}</span>
            </div>
            <div class="flex justify-between">
              <span>Cache Size:</span>
              <span class="font-medium">{{ formattedCacheSize }}</span>
            </div>
            <div class="flex justify-between">
              <span>Offline Ready:</span>
              <span class="font-medium text-success">{{ cacheStats.totalPages > 0 ? 'Yes' : 'No' }}</span>
            </div>
          </div>
        </div>

        <!-- Retry Connection -->
        <div class="space-y-3">
          <button @click="checkConnection" class="btn btn-accent w-full" :disabled="isChecking">
            <span v-if="!isChecking">🔄 Check Connection</span>
            <span v-else class="loading loading-spinner loading-sm"></span>
          </button>

          <div v-if="lastChecked" class="text-xs text-base-content/50">
            Last checked: {{ lastChecked.toLocaleTimeString() }}
          </div>
        </div>

        <!-- Tips -->
        <div class="bg-info/10 border border-info/20 rounded-lg p-4 mt-6">
          <h4 class="font-semibold text-info mb-2">💡 Offline Tips</h4>
          <ul class="text-sm text-left space-y-1">
            <li>• You can still navigate to cached pages</li>
            <li>• Contact forms will be saved and sent when online</li>
            <li>• Your data is safely stored locally</li>
            <li>• The app will automatically sync when reconnected</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useOfflineCache } from '@/composables/useOfflineCache'

const router = useRouter()
const authStore = useAuthStore()
const { cacheStats, formattedCacheSize } = useOfflineCache()

// Local state
const isChecking = ref(false)
const lastChecked = ref<Date | null>(null)
const networkStatus = ref('Checking connection...')

// Computed
const isAuthenticated = computed(() => authStore.isAuthenticated)

// Methods
const checkConnection = async () => {
  isChecking.value = true
  lastChecked.value = new Date()
  
  try {
    // Try to fetch a small resource to test connection
    const response = await fetch('/favicon.ico', { 
      method: 'HEAD',
      cache: 'no-cache'
    })
    
    if (response.ok) {
      networkStatus.value = 'Connection restored!'
      
      // Wait a moment then redirect to home
      setTimeout(() => {
        router.push('/')
      }, 1500)
    } else {
      networkStatus.value = 'Still offline - server not reachable'
    }
  } catch (error) {
    networkStatus.value = 'Still offline - no internet connection'
  } finally {
    isChecking.value = false
  }
}

const updateNetworkStatus = () => {
  if (navigator.onLine) {
    networkStatus.value = 'Connection detected - checking server...'
    checkConnection()
  } else {
    networkStatus.value = 'No internet connection detected'
  }
}

// Auto-check connection periodically
let connectionCheckInterval: number

onMounted(() => {
  updateNetworkStatus()
  
  // Listen for online/offline events
  window.addEventListener('online', updateNetworkStatus)
  window.addEventListener('offline', updateNetworkStatus)
  
  // Check connection every 30 seconds
  connectionCheckInterval = window.setInterval(() => {
    if (navigator.onLine) {
      checkConnection()
    }
  }, 30000)
})

onUnmounted(() => {
  window.removeEventListener('online', updateNetworkStatus)
  window.removeEventListener('offline', updateNetworkStatus)
  
  if (connectionCheckInterval) {
    clearInterval(connectionCheckInterval)
  }
})
</script>

<style scoped>
.offline-view {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
}

.btn {
  transition: all 0.2s ease;
}

.btn:hover {
  transform: translateY(-1px);
}

.alert {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}
</style>
