<template>
  <div class="min-h-screen bg-gradient-to-br from-base-100 via-base-200 to-base-300 flex items-center justify-center px-4 py-8">
    <div class="max-w-4xl mx-auto text-center">
      <!-- Animated Energy Icon -->
      <div class="mb-8 relative">
        <div class="text-9xl mb-4 animate-bounce">
          ⚡
        </div>
        <div class="absolute -top-4 -right-4 text-4xl animate-pulse">
          🌱
        </div>
        <div class="absolute -bottom-2 -left-4 text-3xl animate-ping">
          💡
        </div>
      </div>

      <!-- Error Code -->
      <div class="mb-6">
        <h1 class="text-8xl md:text-9xl font-bold text-primary opacity-20 select-none">
          404
        </h1>
        <h2 class="text-2xl md:text-3xl font-bold text-base-content -mt-4">
          {{ $t('error_404.title') }}
        </h2>
      </div>

      <!-- Funny Message -->
      <div class="mb-8 max-w-2xl mx-auto">
        <p class="text-lg md:text-xl text-base-content/80 mb-4 leading-relaxed">
          {{ $t('error_404.funny_message') }}
        </p>
        <p class="text-base text-base-content/60">
          {{ $t('error_404.description') }}
        </p>
      </div>

      <!-- Search Box -->
      <div class="mb-8 max-w-md mx-auto">
        <div class="form-control">
          <div class="input-group">
            <input 
              v-model="searchQuery"
              type="text" 
              :placeholder="$t('error_404.search_placeholder')"
              class="input input-bordered flex-1"
              @keyup.enter="performSearch"
            />
            <button 
              class="btn btn-primary"
              @click="performSearch"
              :disabled="!searchQuery.trim()"
            >
              <Icon name="search" size="sm" />
              {{ $t('error_404.search_button') }}
            </button>
          </div>
        </div>
      </div>

      <!-- Suggestions -->
      <div class="mb-8">
        <h3 class="text-lg font-semibold text-base-content mb-4">
          {{ $t('error_404.suggestions.title') }}
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl mx-auto">
          <!-- Home -->
          <router-link
            to="/"
            class="btn btn-outline btn-primary flex-col h-auto py-4 px-6 hover:scale-105 transition-transform"
          >
            <Icon name="home" size="xl" class="mb-2" />
            <span class="text-sm">{{ $t('error_404.suggestions.home') }}</span>
          </router-link>

          <!-- Services -->
          <!-- <router-link
            to="/services"
            class="btn btn-outline btn-secondary flex-col h-auto py-4 px-6 hover:scale-105 transition-transform"
          >
            <Icon name="settings" size="xl" class="mb-2" />
            <span class="text-sm">{{ $t('error_404.suggestions.services') }}</span>
          </router-link> -->

          <!-- Contact -->
          <router-link
            to="/contact"
            class="btn btn-outline btn-accent flex-col h-auto py-4 px-6 hover:scale-105 transition-transform"
          >
            <Icon name="phone" size="xl" class="mb-2" />
            <span class="text-sm">{{ $t('error_404.suggestions.contact') }}</span>
          </router-link>

          <!-- About -->
          <!-- <router-link
            to="/about"
            class="btn btn-outline btn-info flex-col h-auto py-4 px-6 hover:scale-105 transition-transform"
          >
            <Icon name="info" size="xl" class="mb-2" />
            <span class="text-sm">{{ $t('common.about') }}</span>
          </router-link> -->
        </div>
      </div>

      <!-- Main CTA -->
      <div class="mb-8">
        <router-link 
          to="/"
          class="btn btn-primary btn-lg gap-2 hover:scale-105 transition-transform"
        >
          <Icon name="arrow-left" size="md" />
          {{ $t('error_404.back_home') }}
        </router-link>
      </div>

      <!-- Energy Tip -->
      <div class="max-w-2xl mx-auto">
        <div class="alert alert-info shadow-lg">
          <Icon name="lightbulb" size="lg" />
          <div class="text-left">
            <p class="text-sm">{{ $t('error_404.energy_tip') }}</p>
          </div>
        </div>
      </div>

      <!-- Floating Energy Elements -->
      <div class="fixed inset-0 pointer-events-none overflow-hidden -z-10">
        <div class="absolute top-1/4 left-1/4 text-6xl opacity-10 animate-float">⚡</div>
        <div class="absolute top-3/4 right-1/4 text-4xl opacity-10 animate-float-delayed">🌱</div>
        <div class="absolute top-1/2 left-1/6 text-5xl opacity-10 animate-float-slow">💡</div>
        <div class="absolute bottom-1/4 right-1/6 text-3xl opacity-10 animate-float">🔋</div>
        <div class="absolute top-1/6 right-1/3 text-4xl opacity-10 animate-float-delayed">🌿</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useHead } from '@vueuse/head'
import Icon from '@/components/common/Icon.vue'

// Composables
const router = useRouter()
const { t } = useI18n()

// Reactive data
const searchQuery = ref('')

// Methods
const performSearch = () => {
  if (!searchQuery.value.trim()) return
  
  // For now, redirect to home with search query
  // In a real app, you'd implement actual search functionality
  router.push({
    path: '/',
    query: { search: searchQuery.value.trim() }
  })
}

// SEO Meta
useHead({
  title: () => t('error_404.title'),
  meta: [
    {
      name: 'description',
      content: () => t('error_404.description')
    },
    {
      name: 'robots',
      content: 'noindex, nofollow'
    }
  ]
})
</script>

<style scoped>
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(5deg); }
}

@keyframes float-delayed {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-15px) rotate(-3deg); }
}

@keyframes float-slow {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-10px) rotate(2deg); }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-delayed {
  animation: float-delayed 8s ease-in-out infinite;
  animation-delay: 2s;
}

.animate-float-slow {
  animation: float-slow 10s ease-in-out infinite;
  animation-delay: 4s;
}
</style>
