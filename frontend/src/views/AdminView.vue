<template>
  <div class="drawer lg:drawer-open min-h-screen bg-base-100">
    <!-- Mobile drawer toggle -->
    <input id="admin-drawer" type="checkbox" class="drawer-toggle" />

    <!-- Drawer content (main content area) -->
    <div class="drawer-content flex flex-col">
      <!-- Enhanced Mobile navbar -->
      <div class="navbar bg-base-100 shadow-lg lg:hidden border-b border-base-200 sticky top-0 z-50">
        <div class="flex-none">
          <label for="admin-drawer" class="btn btn-square btn-ghost hover:bg-primary/10 active:bg-primary/20">
            <Icon name="menu" size="lg" class="transition-transform duration-200" />
          </label>
        </div>
        <div class="flex-1 px-2">
          <div class="flex items-center gap-3">
            <div class="p-1.5 bg-primary/20 rounded-lg">
              <Icon name="settings" size="md" class="text-primary" />
            </div>
            <div class="min-w-0 flex-1">
              <h1 class="text-lg font-bold text-base-content truncate">Admin Dashboard</h1>
              <div class="flex items-center gap-2 mt-0.5">
                <div class="badge badge-success badge-xs gap-1">
                  <div class="w-1.5 h-1.5 bg-success rounded-full animate-pulse"></div>
                  Online
                </div>
                <span class="text-xs text-base-content/60">{{ authStore.user?.name || 'Admin' }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="flex-none">
          <div class="flex items-center gap-2">
            <!-- Back to Site Button (Mobile) -->
            <a href="/" class="btn btn-ghost btn-sm btn-circle" title="Back to Site">
              <Icon name="arrow-left" size="sm" />
            </a>

            <!-- Quick Actions Button -->
            <div class="dropdown dropdown-end">
              <div tabindex="0" role="button" class="btn btn-ghost btn-sm btn-circle">
                <Icon name="ellipsis-vertical" size="md" />
              </div>
              <ul tabindex="0" class="menu menu-sm dropdown-content mt-3 z-[1] p-2 shadow-xl bg-base-100 rounded-box w-64 border border-base-200">
                <li class="menu-title">
                  <span class="text-xs font-semibold text-base-content/70">Quick Actions</span>
                </li>
                <li><a @click="openSystemSettings" class="gap-3"><Icon name="settings" size="sm" />Settings</a></li>
                <li><a @click="openSystemLogs" class="gap-3"><Icon name="document-text" size="sm" />Logs</a></li>
                <li><a @click="openSocketMonitor" class="gap-3"><Icon name="signal" size="sm" />Monitor</a></li>
                <li><a @click="openWorkerManagement" class="gap-3"><Icon name="cog" size="sm" />Worker</a></li>
                <div class="divider my-1"></div>
                <li><a @click="authStore.logout()" class="text-error gap-3"><Icon name="logout" size="sm" />Logout</a></li>
              </ul>
            </div>

            <!-- User Avatar -->
            <div class="dropdown dropdown-end">
              <div tabindex="0" role="button" class="btn btn-ghost btn-circle avatar">
                <div class="w-8 rounded-full bg-primary/20 flex items-center justify-center ring-2 ring-primary/20">
                  <Icon name="user" size="sm" class="text-primary" />
                </div>
              </div>
              <ul tabindex="0" class="menu menu-sm dropdown-content mt-3 z-[1] p-2 shadow-xl bg-base-100 rounded-box w-56 border border-base-200">
                <li class="menu-title">
                  <span class="text-xs font-semibold text-base-content/70">Account</span>
                </li>
                <li><a class="gap-3"><Icon name="user" size="sm" />Profile</a></li>
                <li><a class="gap-3"><Icon name="settings" size="sm" />Preferences</a></li>
                <div class="divider my-1"></div>
                <li><a @click="authStore.logout()" class="text-error gap-3"><Icon name="logout" size="sm" />Sign Out</a></li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- Desktop hero header -->
      <div class="hidden lg:block bg-gradient-to-r from-primary/5 via-secondary/5 to-accent/5 border-b border-base-200">
        <div class="max-w-7xl mx-auto px-6 py-8">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <div class="p-3 bg-primary/10 rounded-xl">
                <Icon name="settings" size="xl" class="text-primary" />
              </div>
              <div>
                <h1 class="text-3xl font-bold text-base-content">
                  Admin Dashboard
                </h1>
                <p class="text-base-content/70 mt-1">
                  System administration and management center
                </p>
              </div>
            </div>
            <div class="flex items-center space-x-3">
              <div class="stats shadow-sm bg-base-100/50 border border-base-200">
                <div class="stat py-2 px-4">
                  <div class="stat-title text-xs">Status</div>
                  <div class="stat-value text-sm text-success">Online</div>
                </div>
              </div>
              <button @click="authStore.logout()" class="btn btn-outline btn-error btn-sm">
                <Icon name="logout" size="sm" />
                Logout
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Main content area -->
      <main class="flex-1 p-4 lg:p-6">
        <!-- Dashboard content will go here -->
        <AdminDashboard />
      </main>
    </div>

    <!-- Enhanced Drawer sidebar -->
    <div class="drawer-side">
      <label for="admin-drawer" aria-label="close sidebar" class="drawer-overlay"></label>
      <aside class="min-h-full w-80 lg:w-72 bg-base-200 border-r border-base-300 flex flex-col">
        <!-- Mobile-optimized Sidebar header -->
        <div class="p-4 border-b border-base-300 bg-base-100/50 backdrop-blur-sm">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <div class="p-2 bg-primary/20 rounded-lg">
                <Icon name="settings" size="lg" class="text-primary" />
              </div>
              <div>
                <h2 class="font-bold text-lg text-base-content">Admin Panel</h2>
                <p class="text-sm text-base-content/70">Management Center</p>
              </div>
            </div>
            <!-- Mobile close button -->
            <label for="admin-drawer" class="btn btn-ghost btn-sm btn-circle lg:hidden">
              <Icon name="close" size="md" />
            </label>
          </div>
        </div>

        <!-- Back to Site Button -->
        <div class="p-4 border-b border-base-300">
          <a href="/" class="btn btn-outline btn-primary w-full gap-2 hover:btn-primary transition-all duration-200">
            <Icon name="arrow-left" size="sm" />
            <span class="font-medium">Back to Site</span>
          </a>
        </div>

        <!-- Enhanced Navigation menu -->
        <div class="flex-1 overflow-y-auto p-4">
          <ul class="menu w-full space-y-1">
            <!-- Dashboard -->
            <li>
              <a class="active bg-primary/10 text-primary border border-primary/20 h-16 lg:h-14 rounded-lg">
                <Icon name="chart-bar" size="lg" />
                <div class="flex flex-col items-start flex-1 min-w-0">
                  <span class="font-medium text-sm lg:text-base">Dashboard</span>
                  <span class="text-xs opacity-70 truncate">Overview & metrics</span>
                </div>
                <div class="badge badge-primary badge-xs lg:hidden">•</div>
              </a>
            </li>

            <!-- User Management -->
            <li>
              <a
                @click="openUserManagement"
                @mouseenter="preloadComponent('UserManagement')"
                class="hover:bg-base-300 active:bg-base-300/70 transition-all duration-200 h-16 lg:h-14 rounded-lg"
              >
                <Icon name="users" size="lg" />
                <div class="flex flex-col items-start flex-1 min-w-0">
                  <span class="font-medium text-sm lg:text-base">User Management</span>
                  <span class="text-xs opacity-70 truncate">Manage users & roles</span>
                </div>
                <Icon name="chevron-right" size="sm" class="opacity-50 lg:hidden" />
              </a>
            </li>

            <!-- System Settings -->
            <li>
              <a
                @click="openSystemSettings"
                @mouseenter="preloadComponent('SystemSettings')"
                class="hover:bg-base-300 active:bg-base-300/70 transition-all duration-200 h-16 lg:h-14 rounded-lg"
              >
                <Icon name="settings" size="lg" />
                <div class="flex flex-col items-start flex-1 min-w-0">
                  <span class="font-medium text-sm lg:text-base">System Settings</span>
                  <span class="text-xs opacity-70 truncate">Configure system</span>
                </div>
                <Icon name="chevron-right" size="sm" class="opacity-50 lg:hidden" />
              </a>
            </li>

            <!-- Database Management -->
            <li>
              <a
                @click="openDatabaseManagement"
                @mouseenter="preloadComponent('DatabaseManagement')"
                class="hover:bg-base-300 active:bg-base-300/70 transition-all duration-200 h-16 lg:h-14 rounded-lg"
              >
                <Icon name="database" size="lg" />
                <div class="flex flex-col items-start flex-1 min-w-0">
                  <span class="font-medium text-sm lg:text-base">Database</span>
                  <span class="text-xs opacity-70 truncate">Manage database</span>
                </div>
                <Icon name="chevron-right" size="sm" class="opacity-50 lg:hidden" />
              </a>
            </li>

            <!-- Security Settings -->
            <li>
              <a
                @click="openSecuritySettings"
                @mouseenter="preloadComponent('SecuritySettings')"
                class="hover:bg-base-300 active:bg-base-300/70 transition-all duration-200 h-16 lg:h-14 rounded-lg"
              >
                <Icon name="shield" size="lg" />
                <div class="flex flex-col items-start flex-1 min-w-0">
                  <span class="font-medium text-sm lg:text-base">Security</span>
                  <span class="text-xs opacity-70 truncate">Security settings</span>
                </div>
                <Icon name="chevron-right" size="sm" class="opacity-50 lg:hidden" />
              </a>
            </li>

            <!-- System Logs -->
            <li>
              <a
                @click="openSystemLogs"
                @mouseenter="preloadComponent('SystemLogs')"
                class="hover:bg-base-300 active:bg-base-300/70 transition-all duration-200 h-16 lg:h-14 rounded-lg"
              >
                <Icon name="document-text" size="lg" />
                <div class="flex flex-col items-start flex-1 min-w-0">
                  <span class="font-medium text-sm lg:text-base">System Logs</span>
                  <span class="text-xs opacity-70 truncate">View system logs</span>
                </div>
                <Icon name="chevron-right" size="sm" class="opacity-50 lg:hidden" />
              </a>
            </li>

            <!-- Socket Monitor -->
            <li>
              <a
                @click="openSocketMonitor"
                @mouseenter="preloadComponent('SocketMonitor')"
                class="hover:bg-base-300 active:bg-base-300/70 transition-all duration-200 h-16 lg:h-14 rounded-lg"
              >
                <Icon name="signal" size="lg" />
                <div class="flex flex-col items-start flex-1 min-w-0">
                  <span class="font-medium text-sm lg:text-base">Socket Monitor</span>
                  <span class="text-xs opacity-70 truncate">Real-time connections</span>
                </div>
                <Icon name="chevron-right" size="sm" class="opacity-50 lg:hidden" />
              </a>
            </li>

            <!-- Worker Management -->
            <li>
              <a
                @click="openWorkerManagement"
                @mouseenter="preloadComponent('WorkerManagement')"
                class="hover:bg-base-300 active:bg-base-300/70 transition-all duration-200 h-16 lg:h-14 rounded-lg"
              >
                <Icon name="cog" size="lg" />
                <div class="flex flex-col items-start flex-1 min-w-0">
                  <span class="font-medium text-sm lg:text-base">Worker Management</span>
                  <span class="text-xs opacity-70 truncate">Background jobs & health</span>
                </div>
                <Icon name="chevron-right" size="sm" class="opacity-50 lg:hidden" />
              </a>
            </li>
          </ul>
        </div>

        <!-- Enhanced Mobile-Optimized Sidebar footer -->
        <div class="p-4 border-t border-base-300 bg-base-100/50 backdrop-blur-sm mt-auto">
          <div class="space-y-3">
            <!-- User Info -->
            <div class="flex items-center gap-3">
              <div class="avatar">
                <div class="w-8 rounded-full bg-primary/20 flex items-center justify-center">
                  <Icon name="user" size="sm" class="text-primary" />
                </div>
              </div>
              <div class="flex-1 min-w-0">
                <div class="font-medium text-sm text-base-content truncate">{{ authStore.user?.name || 'Admin User' }}</div>
                <div class="flex items-center gap-2 text-xs text-base-content/70">
                  <div class="w-2 h-2 bg-success rounded-full animate-pulse"></div>
                  <span>Online</span>
                </div>
              </div>
            </div>

            <!-- Quick Actions for Mobile -->
            <div class="lg:hidden">
              <div class="grid grid-cols-2 gap-2">
                <button @click="openSystemSettings" class="btn btn-ghost btn-sm justify-start gap-2">
                  <Icon name="settings" size="sm" />
                  <span class="text-xs">Settings</span>
                </button>
                <button @click="authStore.logout()" class="btn btn-ghost btn-sm justify-start gap-2 text-error">
                  <Icon name="logout" size="sm" />
                  <span class="text-xs">Logout</span>
                </button>
              </div>
            </div>

            <!-- Desktop Version Info -->
            <div class="hidden lg:flex items-center justify-between text-xs text-base-content/60">
              <span>Admin Panel v2.0</span>
              <span>{{ new Date().getFullYear() }}</span>
            </div>
          </div>
        </div>
      </aside>
    </div>

    <!-- Modal Components -->
    <UserManagement v-if="showUserManagement" @close="showUserManagement = false" />
    <SystemSettings v-if="showSystemSettings" @close="showSystemSettings = false" />
    <DatabaseManagement v-if="showDatabaseManagement" @close="showDatabaseManagement = false" />
    <SecuritySettings v-if="showSecuritySettings" @close="showSecuritySettings = false" />
    <SystemLogs v-if="showSystemLogs" @close="showSystemLogs = false" />
    <SocketMonitor v-if="showSocketMonitor" @close="showSocketMonitor = false" />
    <WorkerManagement v-if="showWorkerManagement" @close="showWorkerManagement = false" />
  </div>
</template>

<script setup lang="ts">
import { ref, defineAsyncComponent, h } from 'vue'
import { useAuthStore } from '@/stores/auth'
import Icon from '@/components/common/Icon.vue'

// Loading component for lazy loaded admin components
const AdminLoadingComponent = {
  setup() {
    return () => h('div', { class: 'flex items-center justify-center p-8' }, [
      h('div', { class: 'text-center space-y-4' }, [
        h('div', { class: 'loading loading-spinner loading-lg text-primary' }),
        h('div', { class: 'text-sm text-base-content/70' }, 'Loading admin component...')
      ])
    ])
  }
}

// Error component for lazy loaded admin components
const AdminErrorComponent = {
  setup() {
    return () => h('div', { class: 'alert alert-error shadow-lg' }, [
      h(Icon, { name: 'warning', size: 'lg' }),
      h('div', {}, [
        h('h3', { class: 'font-bold' }, 'Failed to load admin component'),
        h('div', { class: 'text-sm' }, 'Please refresh the page and try again.')
      ]),
      h('button', {
        class: 'btn btn-sm btn-outline',
        onClick: () => window.location.reload()
      }, [
        h(Icon, { name: 'refresh', size: 'sm' }),
        ' Refresh'
      ])
    ])
  }
}

// Lazy load admin components for better performance with loading states
const AdminDashboard = defineAsyncComponent({
  loader: () => import('@/components/admin/AdminDashboard.vue'),
  loadingComponent: AdminLoadingComponent,
  errorComponent: AdminErrorComponent,
  delay: 200,
  timeout: 10000
})

const UserManagement = defineAsyncComponent({
  loader: () => import('@/components/admin/UserManagement.vue'),
  loadingComponent: AdminLoadingComponent,
  errorComponent: AdminErrorComponent,
  delay: 200,
  timeout: 10000
})

const SystemSettings = defineAsyncComponent({
  loader: () => import('@/components/admin/SystemSettings.vue'),
  loadingComponent: AdminLoadingComponent,
  errorComponent: AdminErrorComponent,
  delay: 200,
  timeout: 10000
})

const DatabaseManagement = defineAsyncComponent({
  loader: () => import('@/components/admin/DatabaseManagement.vue'),
  loadingComponent: AdminLoadingComponent,
  errorComponent: AdminErrorComponent,
  delay: 200,
  timeout: 10000
})

const SecuritySettings = defineAsyncComponent({
  loader: () => import('@/components/admin/SecuritySettings.vue'),
  loadingComponent: AdminLoadingComponent,
  errorComponent: AdminErrorComponent,
  delay: 200,
  timeout: 10000
})

const SystemLogs = defineAsyncComponent({
  loader: () => import('@/components/admin/SystemLogs.vue'),
  loadingComponent: AdminLoadingComponent,
  errorComponent: AdminErrorComponent,
  delay: 200,
  timeout: 10000
})

const SocketMonitor = defineAsyncComponent({
  loader: () => import('@/components/admin/SocketMonitor.vue'),
  loadingComponent: AdminLoadingComponent,
  errorComponent: AdminErrorComponent,
  delay: 200,
  timeout: 10000
})

const WorkerManagement = defineAsyncComponent({
  loader: () => import('@/components/admin/WorkerManagement.vue'),
  loadingComponent: AdminLoadingComponent,
  errorComponent: AdminErrorComponent,
  delay: 200,
  timeout: 10000
})

const authStore = useAuthStore()

// Modal visibility states
const showUserManagement = ref(false)
const showSystemSettings = ref(false)
const showDatabaseManagement = ref(false)
const showSecuritySettings = ref(false)
const showSystemLogs = ref(false)
// const showMemoryMonitor = ref(false) // Removed - performance monitoring disabled
const showSocketMonitor = ref(false)
const showWorkerManagement = ref(false)

// Component preloading for better UX
const preloadedComponents = new Set<string>()

const preloadComponent = async (componentName: string) => {
  if (preloadedComponents.has(componentName)) return

  try {
    switch (componentName) {
      case 'UserManagement':
        await import('@/components/admin/UserManagement.vue')
        break
      case 'SystemSettings':
        await import('@/components/admin/SystemSettings.vue')
        break
      case 'DatabaseManagement':
        await import('@/components/admin/DatabaseManagement.vue')
        break
      case 'SecuritySettings':
        await import('@/components/admin/SecuritySettings.vue')
        break
      case 'SystemLogs':
        await import('@/components/admin/SystemLogs.vue')
        break
      case 'SocketMonitor':
        await import('@/components/admin/SocketMonitor.vue')
        break
      case 'WorkerManagement':
        await import('@/components/admin/WorkerManagement.vue')
        break
    }
    preloadedComponents.add(componentName)
  } catch (error) {
    console.warn(`Failed to preload ${componentName}:`, error)
  }
}

// Helper function to close mobile drawer
const closeMobileDrawer = () => {
  const drawerToggle = document.getElementById('admin-drawer') as HTMLInputElement
  if (drawerToggle) {
    drawerToggle.checked = false
  }
}

// Navigation handlers that close mobile drawer
const openUserManagement = () => {
  showUserManagement.value = true
  closeMobileDrawer()
}

const openSystemSettings = () => {
  showSystemSettings.value = true
  closeMobileDrawer()
}

const openDatabaseManagement = () => {
  showDatabaseManagement.value = true
  closeMobileDrawer()
}

const openSecuritySettings = () => {
  showSecuritySettings.value = true
  closeMobileDrawer()
}

const openSystemLogs = () => {
  showSystemLogs.value = true
  closeMobileDrawer()
}

const openSocketMonitor = () => {
  showSocketMonitor.value = true
  closeMobileDrawer()
}

const openWorkerManagement = () => {
  showWorkerManagement.value = true
  closeMobileDrawer()
}


</script>

<style scoped>
.btn.justify-start {
  justify-content: flex-start;
}

/* Enhanced shadow effects */
.shadow-3xl {
  box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
}

/* Mobile-specific optimizations */
@media (max-width: 1024px) {
  /* Improve touch targets for mobile */
  .menu li > a {
    min-height: 4rem;
    padding: 0.75rem 1rem;
  }

  /* Better tap feedback */
  .btn:active {
    transform: scale(0.98);
  }

  /* Optimize drawer width for mobile */
  .drawer-side aside {
    width: min(20rem, 85vw);
  }
}

/* Custom scrollbar for sidebar */
.drawer-side aside {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--bc) / 0.2) transparent;
}

.drawer-side aside::-webkit-scrollbar {
  width: 6px;
}

.drawer-side aside::-webkit-scrollbar-track {
  background: transparent;
}

.drawer-side aside::-webkit-scrollbar-thumb {
  background-color: hsl(var(--bc) / 0.2);
  border-radius: 3px;
}

.drawer-side aside::-webkit-scrollbar-thumb:hover {
  background-color: hsl(var(--bc) / 0.3);
}

/* Mobile-optimized modal positioning */
@media (max-width: 768px) {
  .modal-box {
    max-width: calc(100vw - 2rem);
    max-height: calc(100vh - 2rem);
    margin: 1rem;
  }
}

/* Improve focus visibility for accessibility */
.menu li > a:focus-visible {
  outline: 2px solid hsl(var(--p));
  outline-offset: 2px;
}

/* Better active states for touch devices */
@media (hover: none) and (pointer: coarse) {
  .menu li > a:hover {
    background-color: transparent;
  }

  .menu li > a:active {
    background-color: hsl(var(--bc) / 0.1);
    transform: scale(0.98);
  }
}

/* Smooth backdrop blur */
.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

/* Enhanced hover animations */
.group:hover .card {
  transform: translateY(-8px) scale(1.02);
}

/* Gradient text effects */
.text-gradient {
  background: linear-gradient(135deg, #02342b, #eaaa34);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Custom button hover effects */
.btn-enhanced {
  position: relative;
  overflow: hidden;
}

.btn-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-enhanced:hover::before {
  left: 100%;
}

/* Enhanced table styling */
.table th {
  position: sticky;
  top: 0;
  z-index: 10;
}

/* Smooth transitions for all interactive elements */
* {
  transition-property: transform, box-shadow, background-color, border-color, opacity;
  transition-duration: 200ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Custom scrollbar for better aesthetics */
.overflow-x-auto::-webkit-scrollbar {
  height: 6px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: theme('colors.base-200');
  border-radius: 3px;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: theme('colors.base-300');
  border-radius: 3px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: theme('colors.base-content/30');
}
</style>
