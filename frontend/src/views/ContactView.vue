<template>
  <div class="min-h-screen bg-base-100 contact-page">


    <!-- Contact Form Section -->
    <section class="py-20 bg-base-100">
      <div class="max-w-7xl mx-auto px-6 lg:px-8">
        <div class="text-center mb-12">
          <h1 class="text-4xl md:text-5xl font-bold mb-4 text-base-content">{{ $t('contact.title') }}</h1>
          <p class="text-lg text-base-content/70 max-w-3xl mx-auto">{{ $t('contact.section_subtitle') }}</p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-16">
          <!-- Contact Form -->
          <div class="relative">
            <!-- Background decoration -->
            <div class="absolute -inset-4 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-3xl blur-xl opacity-30"></div>

            <div class="relative card bg-base-100/80 backdrop-blur-sm shadow-2xl border border-base-200/50 hover:shadow-3xl transition-all duration-500">
              <div class="card-body p-8 border border-base-200/30 rounded-2xl bg-base-100/50">
                <div class="flex items-center gap-3 mb-8">
                  <div class="p-3 bg-gradient-to-br from-primary/20 to-primary/10 rounded-xl">
                    <Icon name="message" size="lg" class="text-primary" />
                  </div>
                  <h3 class="text-2xl font-bold text-base-content">{{ $t('contact.form_title') }}</h3>
                </div>

                <form @submit.prevent="submitForm" class="space-y-6">
                  <!-- Submission Progress -->
                  <div v-if="submissionState !== 'idle'" class="mb-6">
                    <!-- Progress Bar -->
                    <div v-if="submissionState === 'validating' || submissionState === 'submitting'" class="mb-4">
                      <div class="flex items-center justify-between mb-2">
                        <span class="text-sm font-medium text-base-content">
                          {{ submissionState === 'validating' ? $t('contact.progress.validating') : $t('contact.progress.submitting') }}
                        </span>
                        <span class="text-sm text-base-content/70">{{ submissionProgress }}%</span>
                      </div>
                      <div class="w-full bg-base-200 rounded-full h-2">
                        <div
                          class="bg-gradient-to-r from-primary to-primary-focus h-2 rounded-full transition-all duration-500 ease-out"
                          :style="{ width: `${submissionProgress}%` }"
                        ></div>
                      </div>
                    </div>

                    <!-- Success Message -->
                    <div v-if="submissionState === 'success' && submitMessage" class="alert alert-success shadow-lg border border-success/20 bg-success/10 animate-fade-in-up">
                      <div class="flex items-center gap-3">
                        <div class="p-2 bg-success/20 rounded-full">
                          <Icon name="check-circle" size="md" class="text-success animate-bounce" />
                        </div>
                        <div class="flex-1">
                          <div class="font-medium">{{ $t('contact.progress.success') }}</div>
                          <div class="text-sm opacity-80">{{ submitMessage }}</div>
                        </div>
                      </div>
                    </div>

                    <!-- Error Message -->
                    <div v-if="submissionState === 'error' && submitError" class="alert alert-error shadow-lg border border-error/20 bg-error/10 animate-fade-in-up">
                      <div class="flex items-center gap-3">
                        <div class="p-2 bg-error/20 rounded-full">
                          <Icon name="x-circle" size="md" class="text-error animate-pulse" />
                        </div>
                        <div class="flex-1">
                          <div class="font-medium">{{ $t('contact.progress.error') }}</div>
                          <div class="text-sm opacity-80">{{ submitError }}</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="form-control">
                    <label class="label">
                      <span class="label-text font-semibold text-base-content">{{ $t('contact.name') }}</span>
                      <span class="label-text-alt text-error">*</span>
                    </label>
                    <div class="relative">
                      <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Icon name="user" size="sm" class="text-base-content/40" />
                      </div>
                      <input
                        type="text"
                        v-model="form.name"
                        class="input input-bordered w-full pl-10 focus:input-primary transition-all duration-200 no-zoom"
                        :class="{ 'input-error': formErrors.name }"
                        :placeholder="$t('contact.name_placeholder')"
                        :disabled="isFormDisabled"
                        @blur="validateForm"
                        required
                      />
                    </div>
                    <div v-if="formErrors.name" class="label">
                      <span class="label-text-alt text-error">{{ formErrors.name }}</span>
                    </div>
                  </div>

                  <div class="form-control">
                    <label class="label">
                      <span class="label-text font-semibold text-base-content">{{ $t('contact.email') }}</span>
                      <span class="label-text-alt text-error">*</span>
                    </label>
                    <div class="relative">
                      <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Icon name="email" size="sm" class="text-base-content/40" />
                      </div>
                      <input
                        type="email"
                        v-model="form.email"
                        class="input input-bordered w-full pl-10 focus:input-primary transition-all duration-200 no-zoom"
                        :class="{ 'input-error': formErrors.email }"
                        :placeholder="$t('contact.email_placeholder')"
                        :disabled="isFormDisabled"
                        @blur="validateForm"
                        required
                      />
                    </div>
                    <div v-if="formErrors.email" class="label">
                      <span class="label-text-alt text-error">{{ formErrors.email }}</span>
                    </div>
                  </div>

                  <div class="form-control">
                    <label class="label">
                      <span class="label-text font-semibold text-base-content">{{ $t('contact.phone') }}</span>
                      <span class="label-text-alt text-base-content/60">{{ $t('contact.optional') }}</span>
                    </label>
                    <div class="flex gap-2">
                      <!-- Country Code Selector -->
                      <div class="dropdown">
                        <div tabindex="0" role="button" class="btn btn-outline min-w-fit">
                          <span class="text-lg">{{ countryCodes.find(c => c.code === form.countryCode)?.flag }}</span>
                          <span class="text-sm">{{ form.countryCode }}</span>
                          <Icon name="chevron-down" size="xs" />
                        </div>
                        <ul tabindex="0" class="dropdown-content menu bg-base-100/95 backdrop-blur-md rounded-xl z-[1] w-80 p-2 shadow-2xl border border-base-200/50 max-h-60 overflow-y-auto">
                          <li v-for="country in countryCodes" :key="country.code">
                            <a @click="form.countryCode = country.code" class="flex items-center gap-3 py-3 px-3 hover:bg-base-200/70 rounded-lg transition-colors duration-200 min-h-fit">
                              <span class="text-lg flex-shrink-0">{{ country.flag }}</span>
                              <div class="flex-1 min-w-0">
                                <div class="font-medium text-sm leading-tight truncate">{{ country.name }}</div>
                                <div class="text-xs opacity-70 mt-0.5">{{ country.code }}</div>
                              </div>
                              <div v-if="form.countryCode === country.code" class="flex-shrink-0">
                                <Icon name="check" size="sm" class="text-primary" />
                              </div>
                            </a>
                          </li>
                        </ul>
                      </div>

                      <!-- Phone Number Input -->
                      <div class="relative flex-1">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <Icon name="phone" size="sm" class="text-base-content/40" />
                        </div>
                        <input
                          type="tel"
                          v-model="form.phone"
                          class="input input-bordered w-full pl-10 focus:input-primary transition-all duration-200 no-zoom"
                          :class="{ 'input-error': formErrors.phone }"
                          :placeholder="$t('contact.phone_placeholder')"
                          :disabled="isFormDisabled"
                          @blur="validateForm"
                        />
                      </div>
                    </div>
                    <div v-if="formErrors.phone" class="label">
                      <span class="label-text-alt text-error">{{ formErrors.phone }}</span>
                    </div>
                  </div>

                  <div class="form-control">
                    <label class="label">
                      <span class="label-text font-semibold text-base-content">{{ $t('contact.message') }}</span>
                      <span class="label-text-alt text-error">*</span>
                    </label>
                    <textarea
                      v-model="form.message"
                      class="textarea textarea-bordered h-32 focus:textarea-primary transition-all duration-200 resize-none no-zoom"
                      :class="{ 'textarea-error': formErrors.message }"
                      :placeholder="$t('contact.message_placeholder')"
                      :disabled="isFormDisabled"
                      @blur="validateForm"
                      required
                    ></textarea>
                    <div class="label">
                      <span v-if="formErrors.message" class="label-text-alt text-error">{{ formErrors.message }}</span>
                      <span v-else class="label-text-alt text-base-content/60">{{ form.message.length }}/10 {{ $t('contact.min_chars') }}</span>
                    </div>
                  </div>

                  <div class="form-control mt-8">
                    <button
                      type="submit"
                      class="btn btn-lg w-full group shadow-lg hover:shadow-xl transition-all duration-300"
                      :class="{
                        'btn-primary': submissionState === 'idle',
                        'btn-info': submissionState === 'validating' || submissionState === 'submitting',
                        'btn-success': submissionState === 'success',
                        'btn-error': submissionState === 'error'
                      }"
                      :disabled="isFormDisabled || submissionState === 'success'"
                    >
                      <!-- Idle State -->
                      <template v-if="submissionState === 'idle'">
                        <Icon name="send" size="sm" class="mr-2 group-hover:scale-110 transition-transform" />
                        <span>{{ $t('contact.send_button') }}</span>
                      </template>

                      <!-- Validating State -->
                      <template v-else-if="submissionState === 'validating'">
                        <span class="loading loading-spinner loading-sm mr-2"></span>
                        <span>{{ $t('contact.progress.validating') }}</span>
                      </template>

                      <!-- Submitting State -->
                      <template v-else-if="submissionState === 'submitting'">
                        <span class="loading loading-dots loading-sm mr-2"></span>
                        <span>{{ $t('contact.progress.submitting') }}</span>
                      </template>

                      <!-- Success State -->
                      <template v-else-if="submissionState === 'success'">
                        <Icon name="check" size="sm" class="mr-2 animate-bounce" />
                        <span>{{ $t('contact.progress.sent') }}</span>
                      </template>

                      <!-- Error State -->
                      <template v-else-if="submissionState === 'error'">
                        <Icon name="refresh" size="sm" class="mr-2" />
                        <span>{{ $t('contact.progress.retry') }}</span>
                      </template>
                    </button>
                  </div>

                  <!-- reCAPTCHA v3 Verification -->
                  <div class="form-control">
                    <RecaptchaV3
                      ref="captchaRef"
                      v-model="captchaVerified"
                      action="contact_form"
                      @verified="handleCaptchaVerified"
                      @error="handleCaptchaError"
                    />
                    <div v-if="formErrors.captcha" class="label">
                      <span class="label-text-alt text-error">{{ formErrors.captcha }}</span>
                    </div>
                  </div>

                  <!-- Honeypot field for spam protection (hidden from users) -->
                  <div class="hidden">
                    <label for="website">Website (leave blank):</label>
                    <input
                      type="text"
                      id="website"
                      name="website"
                      v-model="form.honeypot"
                      tabindex="-1"
                      autocomplete="off"
                    />
                  </div>

                  <div class="text-center mt-4">
                    <p class="text-sm text-base-content/60">
                      {{ $t('contact.form_note') }}
                    </p>
                  </div>
                </form>

                <!-- Submitted Data Display - Mobile Optimized -->
                <div v-if="isFormSubmitted && submittedData" class="mt-6 md:mt-8 p-4 md:p-6 bg-success/10 border border-success/20 rounded-2xl submitted-data-display">
                  <!-- Header -->
                  <div class="flex items-center gap-3 mb-4 md:mb-6">
                    <div class="w-10 h-10 md:w-12 md:h-12 bg-success/20 rounded-full flex items-center justify-center flex-shrink-0">
                      <Icon name="check-circle" size="md" class="text-success" />
                    </div>
                    <h3 class="text-lg md:text-xl font-semibold text-success">{{ $t('contact.submitted.title') }}</h3>
                  </div>

                  <!-- Submitted Data -->
                  <div class="space-y-4 md:space-y-3">
                    <!-- Contact Information Cards -->
                    <div class="space-y-3 md:space-y-0 md:grid md:grid-cols-2 md:gap-4">
                      <!-- Name -->
                      <div class="bg-base-100/30 p-3 rounded-lg">
                        <span class="block text-xs font-medium text-base-content/60 uppercase tracking-wide mb-1">{{ $t('contact.name') }}</span>
                        <span class="text-sm md:text-base font-medium text-base-content">{{ submittedData.name }}</span>
                      </div>

                      <!-- Email -->
                      <div class="bg-base-100/30 p-3 rounded-lg">
                        <span class="block text-xs font-medium text-base-content/60 uppercase tracking-wide mb-1">{{ $t('contact.email') }}</span>
                        <span class="text-sm md:text-base font-medium text-base-content break-all">{{ submittedData.email }}</span>
                      </div>

                      <!-- Phone (if provided) -->
                      <div v-if="submittedData.phone" class="bg-base-100/30 p-3 rounded-lg">
                        <span class="block text-xs font-medium text-base-content/60 uppercase tracking-wide mb-1">{{ $t('contact.phone') }}</span>
                        <span class="text-sm md:text-base font-medium text-base-content">{{ submittedData.phone }}</span>
                      </div>

                      <!-- Submission Time -->
                      <div class="bg-base-100/30 p-3 rounded-lg">
                        <span class="block text-xs font-medium text-base-content/60 uppercase tracking-wide mb-1">{{ $t('contact.submitted.time') }}</span>
                        <span class="text-sm md:text-base font-medium text-base-content">{{ new Date(submittedData.submittedAt).toLocaleString() }}</span>
                      </div>
                    </div>

                    <!-- Message -->
                    <div class="bg-base-100/30 p-4 rounded-lg">
                      <span class="block text-xs font-medium text-base-content/60 uppercase tracking-wide mb-2">{{ $t('contact.message') }}</span>
                      <p class="text-sm md:text-base text-base-content leading-relaxed whitespace-pre-wrap">{{ submittedData.message }}</p>
                    </div>
                  </div>

                  <!-- Action Buttons -->
                  <div class="mt-6 flex flex-col sm:flex-row gap-3">
                    <button @click="resetForm" class="btn btn-outline btn-success btn-sm md:btn-md gap-2 flex-1 sm:flex-none">
                      <Icon name="plus" size="sm" />
                      {{ $t('contact.submitted.new_message') }}
                    </button>
                    <a href="mailto:<EMAIL>" @click="trackContactAction($event, 'email')" class="btn btn-primary btn-sm md:btn-md gap-2 flex-1 sm:flex-none">
                      <Icon name="email" size="sm" />
                      {{ $t('contact.submitted.direct_email') }}
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Contact Information -->
          <div class="space-y-8">
            <!-- Contact Details Card -->
            <div class="card bg-gradient-to-br from-secondary/5 to-secondary/10 border border-secondary/20 shadow-xl hover:shadow-2xl transition-all duration-300">
              <div class="card-body p-8">
                <div class="flex items-center gap-3 mb-6">
                  <div class="p-3 bg-gradient-to-br from-secondary/20 to-secondary/10 rounded-xl">
                    <Icon name="info" size="lg" class="text-secondary" />
                  </div>
                  <h3 class="text-2xl font-bold text-base-content">{{ $t('contact.info_title') }}</h3>
                </div>

                <div class="grid gap-4 md:gap-6">
                  <!-- Email Contact -->
                  <div class="group contact-card p-4 md:p-6 rounded-2xl bg-base-100/80 backdrop-blur-sm border border-base-200/50 hover:border-primary/50 transition-all duration-300 hover:shadow-xl hover:-translate-y-1">
                    <div class="flex flex-col sm:flex-row sm:items-center gap-4 contact-info-mobile">
                      <div class="flex items-center gap-4 flex-1">
                        <div class="w-12 h-12 md:w-14 md:h-14 bg-primary/10 rounded-xl flex items-center justify-center group-hover:bg-primary/20 transition-all duration-300 group-hover:scale-110">
                          <Icon name="email" size="md" class="text-primary" />
                        </div>
                        <div class="flex-1 min-w-0">
                          <h3 class="font-semibold text-base-content contact-title-mobile">{{ $t('contact.email_label') }}</h3>
                          <p class="text-base-content/70 text-sm contact-subtitle-mobile truncate"><EMAIL></p>
                        </div>
                      </div>
                      <a
                        href="mailto:<EMAIL>"
                        @click="trackContactAction($event, 'email')"
                        class="btn btn-primary btn-sm md:btn-md contact-button-mobile gap-2 min-h-[44px]"
                      >
                        <Icon name="email" size="sm" />
                        <span class="hidden sm:inline">{{ $t('contact.email_us') }}</span>
                      </a>
                    </div>
                  </div>

                  <!-- Phone Contact -->
                  <div class="group contact-card p-4 md:p-6 rounded-2xl bg-base-100/80 backdrop-blur-sm border border-base-200/50 hover:border-secondary/50 transition-all duration-300 hover:shadow-xl hover:-translate-y-1">
                    <div class="flex flex-col sm:flex-row sm:items-center gap-4 contact-info-mobile">
                      <div class="flex items-center gap-4 flex-1">
                        <div class="w-12 h-12 md:w-14 md:h-14 bg-secondary/10 rounded-xl flex items-center justify-center group-hover:bg-secondary/20 transition-all duration-300 group-hover:scale-110">
                          <Icon name="phone" size="md" class="text-secondary" />
                        </div>
                        <div class="flex-1 min-w-0">
                          <h3 class="font-semibold text-base-content contact-title-mobile">{{ $t('contact.phone_label') }}</h3>
                          <div class="space-y-1">
                            <p class="text-base-content/70 text-sm contact-subtitle-mobile">+351 910 907 776</p>
                            <p class="text-base-content/70 text-sm contact-subtitle-mobile">+351 938 235 730</p>
                          </div>
                        </div>
                      </div>
                      <div class="flex flex-col gap-2">
                        <a
                          href="tel:+351910907776"
                          @click="trackContactAction($event, 'phone')"
                          class="btn btn-secondary btn-sm md:btn-md contact-button-mobile gap-2 min-h-[44px]"
                        >
                          <Icon name="phone" size="sm" />
                          <span class="hidden sm:inline">{{ $t('contact.call_now') }}</span>
                        </a>
                        <a
                          href="tel:+351938235730"
                          @click="trackContactAction($event, 'phone')"
                          class="btn btn-secondary btn-sm md:btn-md contact-button-mobile gap-2 min-h-[44px]"
                        >
                          <Icon name="phone" size="sm" />
                          <span class="hidden sm:inline">{{ $t('contact.call_now') }}</span>
                        </a>
                      </div>
                    </div>
                  </div>

                  <!-- WhatsApp Contact -->
                  <div class="group contact-card p-4 md:p-6 rounded-2xl bg-base-100/80 backdrop-blur-sm border border-base-200/50 hover:border-success/50 transition-all duration-300 hover:shadow-xl hover:-translate-y-1">
                    <div class="flex flex-col sm:flex-row sm:items-center gap-4 contact-info-mobile">
                      <div class="flex items-center gap-4 flex-1">
                        <div class="w-12 h-12 md:w-14 md:h-14 bg-success/10 rounded-xl flex items-center justify-center group-hover:bg-success/20 transition-all duration-300 group-hover:scale-110">
                          <svg class="w-6 h-6 text-success" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.525 3.488"/>
                          </svg>
                        </div>
                        <div class="flex-1 min-w-0">
                          <h3 class="font-semibold text-base-content contact-title-mobile">{{ $t('contact.whatsapp_label') }}</h3>
                          <p class="text-base-content/70 text-sm contact-subtitle-mobile">{{ $t('contact.whatsapp_description') }}</p>
                        </div>
                      </div>
                      <a
                        :href="whatsappContactUrl"
                        target="_blank"
                        rel="noopener noreferrer"
                        @click="trackContactAction($event, 'whatsapp')"
                        class="btn btn-success btn-sm md:btn-md contact-button-mobile gap-2 min-h-[44px]"
                      >
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.525 3.488"/>
                        </svg>
                        <span class="hidden sm:inline">{{ $t('contact.message_whatsapp') }}</span>
                      </a>
                    </div>
                  </div>

                  <!-- Address Contact -->
                  <!-- <div class="group contact-card p-4 md:p-6 rounded-2xl bg-base-100/80 backdrop-blur-sm border border-base-200/50 hover:border-accent/50 transition-all duration-300 hover:shadow-xl hover:-translate-y-1">
                    <div class="flex flex-col sm:flex-row sm:items-center gap-4 contact-info-mobile">
                      <div class="flex items-center gap-4 flex-1">
                        <div class="w-12 h-12 md:w-14 md:h-14 bg-accent/10 rounded-xl flex items-center justify-center group-hover:bg-accent/20 transition-all duration-300 group-hover:scale-110">
                          <Icon name="map-pin" size="md" class="text-accent" />
                        </div>
                        <div class="flex-1 min-w-0">
                          <h3 class="font-semibold text-base-content contact-title-mobile">{{ $t('contact.address_label') }}</h3>
                          <p class="text-base-content/70 text-sm contact-subtitle-mobile">{{ $t('contact.address') }}</p>
                        </div>
                      </div>
                      <a
                        :href="googleMapsUrl"
                        target="_blank"
                        rel="noopener noreferrer"
                        @click="trackContactAction($event, 'address')"
                        class="btn btn-accent btn-sm md:btn-md contact-button-mobile gap-2 min-h-[44px]"
                      >
                        <Icon name="map-pin" size="sm" />
                        <span class="hidden sm:inline">{{ $t('contact.view_map') }}</span>
                      </a>
                    </div>
                  </div> -->


                </div>
              </div>
            </div>

            <!-- Business Hours Card -->
            <div class="card bg-gradient-to-br from-warning/5 to-warning/10 border border-warning/20 shadow-xl hover:shadow-2xl transition-all duration-300">
              <div class="card-body p-8">
                <div class="flex items-center gap-3 mb-6">
                  <div class="p-3 bg-gradient-to-br from-warning/20 to-warning/10 rounded-xl">
                    <Icon name="clock" size="lg" class="text-warning" />
                  </div>
                  <h3 class="text-2xl font-bold text-base-content">{{ $t('contact.hours_title') }}</h3>
                </div>

                <div class="space-y-4">
                  <div class="flex justify-between items-center p-3 rounded-lg hover:bg-base-200/50 transition-colors">
                    <span class="font-medium text-base-content">{{ $t('contact.weekdays') }}</span>
                    <span class="text-base-content/70">{{ $t('contact.weekdays_hours') }}</span>
                  </div>
                  <div class="flex justify-between items-center p-3 rounded-lg hover:bg-base-200/50 transition-colors">
                    <span class="font-medium text-base-content">{{ $t('contact.saturday') }}</span>
                    <span class="text-base-content/70">{{ $t('contact.saturday_hours') }}</span>
                  </div>
                  <div class="flex justify-between items-center p-3 rounded-lg hover:bg-base-200/50 transition-colors">
                    <span class="font-medium text-base-content">{{ $t('contact.sunday') }}</span>
                    <span class="text-base-content/70">{{ $t('contact.sunday_hours') }}</span>
                  </div>
                </div>

              </div>
            </div>

          </div>
        </div>
      </div>
    </section>

    <!-- Success Modal - True Full Screen Design -->
    <div v-if="showSuccessModal" class="fixed inset-0 z-50 success-modal overflow-hidden">
      <!-- Backdrop -->
      <div class="absolute inset-0 bg-black/60 backdrop-blur-sm" @click="closeSuccessModal"></div>

      <!-- Modal Content Container -->
      <div class="relative w-full h-full flex items-center justify-center success-modal-container">
        <!-- Desktop Modal -->
        <div class="hidden md:block w-full max-w-lg bg-base-100 rounded-2xl shadow-2xl overflow-hidden success-modal-content mx-6">

          <!-- Success Content -->
          <div class="p-8 text-center">

            <!-- Animated Success Icon -->
            <div class="relative mb-8">
              <div class="w-28 h-28 bg-gradient-to-br from-success/20 to-success/10 rounded-full flex items-center justify-center mx-auto mb-2 success-icon-container">
                <div class="w-20 h-20 bg-success/20 rounded-full flex items-center justify-center">
                  <Icon name="check-circle" size="xl" class="text-success success-icon" />
                </div>
              </div>
              <!-- Animated checkmark -->
              <div class="absolute inset-0 flex items-center justify-center">
                <div class="success-checkmark"></div>
              </div>
            </div>

            <!-- Success Message -->
            <h2 class="text-3xl font-bold text-base-content mb-4 leading-tight">
              {{ $t('contact.success.title') }}
            </h2>
            <p class="text-lg text-base-content/80 mb-8 leading-relaxed max-w-sm mx-auto">
              {{ $t('contact.success.message') }}
            </p>

            <!-- Quick Contact Actions -->
            <div class="bg-base-200/30 rounded-2xl p-6 mb-8">
              <p class="text-sm font-medium text-base-content/70 mb-4">{{ $t('contact.success.contact_info') }}</p>
              <div class="grid grid-cols-2 gap-3">
                <a
                  href="mailto:<EMAIL>"
                  @click="trackContactAction($event, 'email')"
                  class="btn btn-primary btn-md gap-2 w-full"
                >
                  <Icon name="email" size="sm" />
                  <span class="hidden lg:inline">{{ $t('contact.email_us') }}</span>
                  <span class="lg:hidden">Email</span>
                </a>
                <a
                  href="tel:+351910907776"
                  @click="trackContactAction($event, 'phone')"
                  class="btn btn-secondary btn-md gap-2 w-full"
                >
                  <Icon name="phone" size="sm" />
                  <span class="hidden lg:inline">{{ $t('contact.call_now') }}</span>
                  <span class="lg:hidden">Call</span>
                </a>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="space-y-3">
              <button @click="resetForm" class="btn btn-outline btn-success w-full gap-2">
                <Icon name="plus" size="sm" />
                <span class="hidden sm:inline">{{ $t('contact.submitted.new_message') }}</span>
                <span class="sm:hidden text-xs">New</span>
              </button>
              <button @click="closeSuccessModal" class="btn btn-primary w-full">
                <span class="hidden sm:inline">{{ $t('contact.success.close') }}</span>
                <span class="sm:hidden text-xs">Close</span>
              </button>
            </div>
          </div>
        </div>

        <!-- Mobile Full Screen Modal -->
        <div class="md:hidden w-full h-full bg-base-100 success-modal-mobile">
          <!-- Mobile Content - No Header, No Scroll -->
          <div class="flex flex-col h-full justify-center px-6 py-8">
            <!-- Centered Content -->
            <div class="flex-1 flex flex-col justify-center">
              <div class="text-center max-w-sm mx-auto space-y-6">

                <!-- Animated Success Icon -->
                <div class="relative mb-6">
                  <div class="w-20 h-20 bg-gradient-to-br from-success/20 to-success/10 rounded-full flex items-center justify-center mx-auto success-icon-container">
                    <div class="w-14 h-14 bg-success/20 rounded-full flex items-center justify-center">
                      <Icon name="check-circle" size="lg" class="text-success success-icon" />
                    </div>
                  </div>
                  <!-- Animated checkmark -->
                  <div class="absolute inset-0 flex items-center justify-center">
                    <div class="success-checkmark"></div>
                  </div>
                </div>

                <!-- Success Message -->
                <div class="space-y-3">
                  <h2 class="text-xl font-bold text-base-content leading-tight">
                    {{ $t('contact.success.title') }}
                  </h2>
                  <p class="text-sm text-base-content/80 leading-relaxed">
                    {{ $t('contact.success.message') }}
                  </p>
                </div>

                <!-- Quick Contact Actions -->
                <div class="bg-base-200/30 rounded-xl p-4 space-y-3">
                  <p class="text-xs font-medium text-base-content/70 mb-3">{{ $t('contact.success.contact_info') }}</p>
                  <div class="space-y-2">
                    <a
                      href="mailto:<EMAIL>"
                      @click="trackContactAction($event, 'email')"
                      class="btn btn-primary btn-sm gap-2 w-full h-12"
                    >
                      <Icon name="email" size="sm" />
                      <span class="text-xs">Email</span>
                    </a>
                    <a
                      href="tel:+351910907776"
                      @click="trackContactAction($event, 'phone')"
                      class="btn btn-secondary btn-sm gap-2 w-full h-12"
                    >
                      <Icon name="phone" size="sm" />
                      <span class="text-xs">Call</span>
                    </a>
                  </div>
                </div>
              </div>
            </div>

            <!-- Fixed Bottom Actions -->
            <div class="pt-6 space-y-3 max-w-sm mx-auto w-full">
              <button @click="resetForm" class="btn btn-outline btn-success w-full gap-2 h-12">
                <Icon name="plus" size="sm" />
                <span class="text-sm">{{ $t('contact.submitted.new_message') }}</span>
              </button>
              <button @click="closeSuccessModal" class="btn btn-primary w-full h-12">
                <span class="text-sm">{{ $t('contact.success.close') }}</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useOfflineStore } from '@/stores/offline'
import { usePWA } from '@/composables/usePWA'


import Icon from '@/components/common/Icon.vue'
import RecaptchaV3 from '@/components/common/RecaptchaV3.vue'

const form = reactive({
  name: '',
  email: '',
  phone: '',
  countryCode: '+351', // Portugal default
  message: '',
  honeypot: '' // Anti-spam honeypot field
})

const formErrors = reactive({
  name: '',
  email: '',
  phone: '',
  message: '',
  captcha: ''
})

const isSubmitting = ref(false)
const submitMessage = ref('')

// Simplified timeout management using Vue's lifecycle
const timeoutRefs = ref<number[]>([])

// Simple timeout helper that auto-cleans on unmount
const createTimeout = (callback: () => void, delay: number): number => {
  const timeoutId = window.setTimeout(callback, delay)
  timeoutRefs.value.push(timeoutId)
  return timeoutId
}

// Auto-cleanup timeouts (called by Vue lifecycle)
const clearTimeouts = () => {
  timeoutRefs.value.forEach(id => clearTimeout(id))
  timeoutRefs.value = []
}
const submitError = ref('')
const isFormDisabled = computed(() => isSubmitting.value || isFormSubmitted.value)

// WhatsApp contact URL
const whatsappContactUrl = computed(() => {
  const phoneNumber = '+351 938 235 730' // Portuguese WhatsApp number
  const message = encodeURIComponent(t('contact.whatsapp_message'))
  return `https://wa.me/${phoneNumber.replace(/[^0-9]/g, '')}?text=${message}`
})

// Google Maps URL
const googleMapsUrl = computed(() => {
  const address = encodeURIComponent(t('contact.address'))
  return `https://www.google.com/maps/search/?api=1&query=${address}`
})

// Track contact actions with proper timing to ensure analytics complete
const trackContactAction = async (event: Event, action: 'email' | 'phone' | 'whatsapp' | 'address') => {
  // Prevent immediate navigation
  event.preventDefault()

  try {
    // Track with backend database only via the existing /track-action endpoint
    const timestamp = new Date().toISOString()

    const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001'
    const response = await fetch(`${API_BASE_URL}/api/v1/contact/track-action`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/vnd.hlenergy.v1+json'
      },
      body: JSON.stringify({
        action,
        timestamp,
        page: 'contact',
        user_agent: navigator.userAgent,
        referrer: document.referrer || null
      })
    })

    if (response.ok) {
      console.log(`📊 Tracked contact action: ${action} - saved to database`)
    } else {
      console.warn(`⚠️ Failed to track contact action: ${response.status} ${response.statusText}`)
    }

    // Small delay to ensure analytics requests complete
    await new Promise(resolve => setTimeout(resolve, 150))

    // Now proceed with the original action
    const target = event.target as HTMLAnchorElement
    const href = target.href || target.closest('a')?.href

    if (href) {
      if (action === 'whatsapp' || action === 'address') {
        // Open external links in new tab
        window.open(href, '_blank', 'noopener,noreferrer')
      } else {
        // For email and phone, use current window
        window.location.href = href
      }
    }
  } catch (error) {
    console.warn('Failed to track contact action:', error)
    // If tracking fails, still proceed with the action
    const target = event.target as HTMLAnchorElement
    const href = target.href || target.closest('a')?.href
    if (href) {
      if (action === 'whatsapp' || action === 'address') {
        window.open(href, '_blank', 'noopener,noreferrer')
      } else {
        window.location.href = href
      }
    }
  }
}

const lastSubmissionTime = ref(0)
const captchaVerified = ref(false)
const captchaToken = ref('')
const captchaRef = ref()

// Enhanced response handling
const submissionState = ref<'idle' | 'validating' | 'submitting' | 'success' | 'error'>('idle')
const submissionProgress = ref(0)
const responseDetails = ref<any>(null)
const showSuccessModal = ref(false)
const submittedData = ref<any>(null)
const isFormSubmitted = ref(false)

// Country codes data
const countryCodes = [
  { code: '+351', name: 'Portugal', flag: '🇵🇹' },
  { code: '+34', name: 'Spain', flag: '🇪🇸' },
  { code: '+33', name: 'France', flag: '🇫🇷' },
  { code: '+49', name: 'Germany', flag: '🇩🇪' },
  { code: '+39', name: 'Italy', flag: '🇮🇹' },
  { code: '+44', name: 'United Kingdom', flag: '🇬🇧' },
  { code: '+1', name: 'United States', flag: '🇺🇸' },
  { code: '+55', name: 'Brazil', flag: '🇧🇷' },
  { code: '+86', name: 'China', flag: '🇨🇳' },
  { code: '+91', name: 'India', flag: '🇮🇳' },
  { code: '+31', name: 'Netherlands', flag: '🇳🇱' },
  { code: '+41', name: 'Switzerland', flag: '🇨🇭' },
  { code: '+43', name: 'Austria', flag: '🇦🇹' },
  { code: '+32', name: 'Belgium', flag: '🇧🇪' },
  { code: '+45', name: 'Denmark', flag: '🇩🇰' },
  { code: '+46', name: 'Sweden', flag: '🇸🇪' },
  { code: '+47', name: 'Norway', flag: '🇳🇴' },
  { code: '+358', name: 'Finland', flag: '🇫🇮' },
  { code: '+353', name: 'Ireland', flag: '🇮🇪' },
  { code: '+420', name: 'Czech Republic', flag: '🇨🇿' }
]

const { t } = useI18n()
const offlineStore = useOfflineStore()
const { isOnline } = usePWA()

// Analytics tracking handled by analyticsService

// Validation functions
const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

const validatePhone = (phone: string, countryCode: string): boolean => {
  if (!phone) return true // Phone is optional

  // Remove spaces, dashes, parentheses
  const cleanPhone = phone.replace(/[\s\-\(\)]/g, '')

  // Country-specific validation patterns
  const validationPatterns: Record<string, RegExp> = {
    '+351': /^[29][0-9]{8}$/, // Portugal: 9 digits starting with 2 or 9
    '+34': /^[67][0-9]{8}$/, // Spain: 9 digits starting with 6 or 7
    '+33': /^[1-9][0-9]{8}$/, // France: 9 digits
    '+49': /^[1-9][0-9]{10,11}$/, // Germany: 11-12 digits
    '+39': /^[3][0-9]{8,9}$/, // Italy: 9-10 digits starting with 3
    '+44': /^[7][0-9]{9}$/, // UK: 10 digits starting with 7
    '+1': /^[2-9][0-9]{9}$/, // US/Canada: 10 digits
    '+55': /^[1-9][0-9]{10}$/, // Brazil: 11 digits
    '+86': /^[1][0-9]{10}$/, // China: 11 digits starting with 1
    '+91': /^[6-9][0-9]{9}$/ // India: 10 digits starting with 6-9
  }

  const pattern = validationPatterns[countryCode]
  if (pattern) {
    return pattern.test(cleanPhone)
  }

  // Fallback: general international format (6-15 digits)
  return /^[0-9]{6,15}$/.test(cleanPhone)
}

const validateForm = (): boolean => {
  let isValid = true

  // Reset errors
  Object.keys(formErrors).forEach(key => {
    formErrors[key as keyof typeof formErrors] = ''
  })

  // Name validation
  if (!form.name.trim()) {
    formErrors.name = t('contact.validation.name_required')
    isValid = false
  } else if (form.name.trim().length < 2) {
    formErrors.name = t('contact.validation.name_min_length')
    isValid = false
  }

  // Email validation
  if (!form.email.trim()) {
    formErrors.email = t('contact.validation.email_required')
    isValid = false
  } else if (!validateEmail(form.email)) {
    formErrors.email = t('contact.validation.email_invalid')
    isValid = false
  }

  // Phone validation (optional but must be valid if provided)
  if (form.phone && !validatePhone(form.phone, form.countryCode)) {
    const countryName = countryCodes.find(c => c.code === form.countryCode)?.name || 'selected country'
    formErrors.phone = t('contact.validation.phone_invalid', { country: countryName })
    isValid = false
  }

  // Message validation
  if (!form.message.trim()) {
    formErrors.message = t('contact.validation.message_required')
    isValid = false
  } else if (form.message.trim().length < 10) {
    formErrors.message = t('contact.validation.message_min_length')
    isValid = false
  }

  // CAPTCHA validation
  if (!captchaVerified.value) {
    formErrors.captcha = t('contact.validation.recaptcha_required')
    isValid = false
  }

  return isValid
}

// Success modal functions
const closeSuccessModal = () => {
  showSuccessModal.value = false
  // Re-enable body scroll
  document.body.classList.remove('modal-open')
  document.documentElement.classList.remove('modal-open')
}

// Watch for modal state changes to control body scroll
watch(showSuccessModal, (isOpen) => {
  if (isOpen) {
    // Prevent body scroll when modal opens
    document.body.classList.add('modal-open')
    document.documentElement.classList.add('modal-open')
    // Store current scroll position
    const scrollY = window.scrollY
    document.body.style.top = `-${scrollY}px`
  } else {
    // Re-enable body scroll when modal closes
    document.body.classList.remove('modal-open')
    document.documentElement.classList.remove('modal-open')
    // Restore scroll position
    const scrollY = document.body.style.top
    document.body.style.top = ''
    if (scrollY) {
      window.scrollTo(0, parseInt(scrollY || '0') * -1)
    }
  }
})

// Reset form function
const resetForm = () => {
  // Reset form data
  form.name = ''
  form.email = ''
  form.phone = ''
  form.message = ''
  form.countryCode = '+351'

  // Reset validation
  Object.keys(formErrors).forEach(key => {
    formErrors[key as keyof typeof formErrors] = ''
  })

  // Reset submission state
  submissionState.value = 'idle'
  submissionProgress.value = 0
  responseDetails.value = null
  submittedData.value = null
  isFormSubmitted.value = false
  submitMessage.value = ''

  // Close success modal
  showSuccessModal.value = false
}

const submitForm = async () => {
  if (isSubmitting.value || isFormSubmitted.value) return

  // Reset states
  submitError.value = ''
  submitMessage.value = ''
  responseDetails.value = null
  submissionState.value = 'validating'
  submissionProgress.value = 0

  // Check honeypot for spam protection
  if (form.honeypot) {
    console.warn('Spam detected: honeypot field filled')
    submitError.value = t('contact.validation.spam_detected')
    submissionState.value = 'error'
    return
  }

  // Rate limiting: prevent submissions within 30 seconds
  const now = Date.now()
  if (now - lastSubmissionTime.value < 30000) {
    submitError.value = t('contact.validation.rate_limit')
    submissionState.value = 'error'
    return
  }

  // Validate form before submission
  submissionProgress.value = 20
  if (!validateForm()) {
    submissionState.value = 'error'

    // Show specific CAPTCHA warning if that's the issue
    if (!captchaVerified.value) {
      // Show warning in CAPTCHA component
      if (captchaRef.value) {
        captchaRef.value.showCaptchaWarning()
        // Scroll to CAPTCHA component
        createTimeout(() => {
          captchaRef.value?.$el?.scrollIntoView({ behavior: 'smooth', block: 'center' })
        }, 100)
      }

      // Also set form error
      createTimeout(() => {
        formErrors.captcha = t('contact.validation.recaptcha_required')
      }, 100)
    }

    return
  }

  try {
    isSubmitting.value = true
    submissionState.value = 'submitting'
    submissionProgress.value = 40

    // Update progress
    submissionProgress.value = 60

    submissionProgress.value = 80
    const result = await offlineStore.submitContactForm({
      name: form.name,
      email: form.email,
      phone: form.phone ? `${form.countryCode} ${form.phone}` : undefined,
      message: form.message,
      source: 'website',
      captchaToken: captchaToken.value
    })

    submissionProgress.value = 100
    submissionState.value = 'success'
    responseDetails.value = result

    // Handle different response scenarios
    if (result.offline) {
      submitMessage.value = isOnline.value
        ? t('contact.response.queued_online')
        : t('contact.response.saved_offline')
    } else if (result.success) {
      submitMessage.value = t('contact.response.success')
    } else {
      submitMessage.value = t('contact.response.submitted')
    }

    // Lead creation is now handled automatically by the backend contact submit endpoint

    // Form completion tracking is handled by the backend

    // Record submission time for rate limiting
    lastSubmissionTime.value = now

    // Store submitted data BEFORE resetting form
    submittedData.value = {
      name: form.name,
      email: form.email,
      phone: form.phone ? `${form.countryCode} ${form.phone}` : '',
      message: form.message,
      submittedAt: new Date().toISOString(),
      result: result
    }
    isFormSubmitted.value = true

    // Reset form AFTER storing submitted data
    Object.assign(form, {
      name: '',
      email: '',
      phone: '',
      countryCode: '+351', // Reset to Portugal default
      message: '',
      honeypot: ''
    })

    // Reset CAPTCHA
    captchaVerified.value = false
    captchaToken.value = ''
    if (captchaRef.value) {
      captchaRef.value.reset()
    }

    // Show success modal
    showSuccessModal.value = true

    // Reset submission state after delay (but keep form submitted state)
    createTimeout(() => {
      submissionState.value = 'idle'
      submissionProgress.value = 0
      submitMessage.value = ''
    }, 5000) // Reset after 5 seconds

    // Log success for demo purposes
    console.log('Contact form submitted successfully:', result)

  } catch (error: any) {
    submissionState.value = 'error'
    submissionProgress.value = 0

    // Enhanced error handling
    if (error.response) {
      // Server responded with error status
      const status = error.response.status
      const data = error.response.data

      switch (status) {
        case 400:
          submitError.value = t('contact.errors.validation_failed')
          break
        case 429:
          submitError.value = t('contact.errors.rate_limit_exceeded')
          break
        case 500:
          submitError.value = t('contact.errors.server_error')
          break
        default:
          submitError.value = t('contact.errors.submission_failed')
      }

      responseDetails.value = { error: data, status }
    } else if (error.request) {
      // Network error
      submitError.value = t('contact.errors.network_error')
      responseDetails.value = { error: 'Network error', type: 'network' }
    } else {
      // Other error
      submitError.value = error.message || t('contact.errors.unknown_error')
      responseDetails.value = { error: error.message, type: 'unknown' }
    }

    console.error('Contact form submission error:', error)
  } finally {
    isSubmitting.value = false

    // Reset progress after delay if there was an error
    if (submissionState.value === 'error') {
      createTimeout(() => {
        submissionProgress.value = 0
      }, 3000)
    }
  }
}

// reCAPTCHA event handlers
const handleCaptchaVerified = (token: string) => {
  captchaVerified.value = true
  captchaToken.value = token
  formErrors.captcha = ''
  console.log('reCAPTCHA verified successfully with token:', token.substring(0, 20) + '...')
}

const handleCaptchaError = (error: string) => {
  captchaVerified.value = false
  captchaToken.value = ''
  formErrors.captcha = error
  console.error('CAPTCHA error:', error)
}

// Component initialization
onMounted(async () => {
  // Page view tracking is handled automatically by the router
})

// Cleanup when component unmounts
// Single cleanup on unmount
onUnmounted(() => {
  // Clear timeouts
  clearTimeouts()

  // Remove modal classes
  document.body.classList.remove('modal-open')
  document.documentElement.classList.remove('modal-open')
  document.body.style.top = ''

  // reCAPTCHA cleanup is handled by the RecaptchaV3 component itself
  console.log('🧹 Contact form: Cleanup completed')
})
</script>

<style scoped>
/* Animation keyframes */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Animation classes */
.animate-fade-in-up {
  animation: fade-in-up 0.8s ease-out forwards;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

/* Animation delays */
.animation-delay-200 {
  animation-delay: 0.2s;
}

.animation-delay-400 {
  animation-delay: 0.4s;
}

.animation-delay-600 {
  animation-delay: 0.6s;
}

.animation-delay-1000 {
  animation-delay: 1s;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

/* Enhanced shadow */
.shadow-3xl {
  box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
}

/* Smooth transitions */
* {
  transition-property: transform, box-shadow, background-color, border-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

/* Form focus states */
.input:focus,
.textarea:focus {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}

/* Prevent iOS Safari zoom on input focus */
.no-zoom {
  font-size: 16px !important; /* Minimum font size to prevent iOS zoom */
}

/* Ensure proper font size on mobile devices */
@media screen and (max-width: 768px) {
  .input,
  .textarea {
    font-size: 16px !important; /* Prevent zoom on iOS Safari */
  }

  /* Maintain proper placeholder font size */
  .input::placeholder,
  .textarea::placeholder {
    font-size: 16px !important;
  }
}

/* Remove any transforms that might cause layout issues */
.input:focus,
.textarea:focus,
.input:active,
.textarea:active {
  transform: none !important;
}

/* Mobile-specific contact information improvements */
@media (max-width: 640px) {
  /* Ensure contact cards have proper spacing on mobile */
  .contact-card {
    padding: 1rem;
  }

  /* Stack contact info vertically on mobile */
  .contact-info-mobile {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 1rem;
  }

  /* Full-width buttons on mobile */
  .contact-button-mobile {
    width: 100%;
    justify-content: center;
    min-height: 44px; /* iOS touch target minimum */
  }

  /* Better text sizing on mobile */
  .contact-title-mobile {
    font-size: 0.875rem; /* 14px */
  }

  .contact-subtitle-mobile {
    font-size: 0.75rem; /* 12px */
  }
}

/* Enhanced glassmorphism effects for contact cards */
.contact-card {
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

[data-theme="hlenergy-dark"] .contact-card {
  background: rgba(10, 31, 27, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.contact-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

[data-theme="hlenergy-dark"] .contact-card:hover {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
}

/* Icon container glass effects */
.contact-card .w-12,
.contact-card .w-14 {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

[data-theme="hlenergy-dark"] .contact-card .w-12,
[data-theme="hlenergy-dark"] .contact-card .w-14 {
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Backdrop blur support */
@supports (backdrop-filter: blur(12px)) {
  .backdrop-blur-sm {
    backdrop-filter: blur(12px);
  }
}

/* Fallback for browsers without backdrop-filter */
@supports not (backdrop-filter: blur(12px)) {
  .backdrop-blur-sm {
    background-color: rgba(255, 255, 255, 0.8);
  }

  .dark .backdrop-blur-sm {
    background-color: rgba(30, 41, 59, 0.8);
  }
}

/* Enhanced shadow effects with brand colors */

/* Success Modal Styles */
.success-modal {
  animation: modalFadeIn 0.3s ease-out;
  /* Prevent any scrolling */
  overflow: hidden;
  /* Ensure full coverage */
  width: 100vw;
  height: 100vh;
  height: 100dvh;
}

.success-modal-container {
  /* Ensure no gaps or margins */
  padding: 0;
  margin: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* Desktop Modal */
.success-modal-content {
  animation: modalSlideUp 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  transform-origin: center bottom;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Mobile Full Screen Modal */
.success-modal-mobile {
  animation: modalSlideUpMobile 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  /* Ensure true full screen with no scroll */
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  height: 100dvh; /* Dynamic viewport height for mobile browsers */
  max-height: 100vh;
  max-height: 100dvh;
  margin: 0;
  padding: 0;
  border-radius: 0;
  overflow: hidden;
  /* Prevent any content overflow */
  box-sizing: border-box;
}

/* Success Icon Animations */
.success-icon-container {
  animation: iconPulse 0.6s ease-out 0.2s both;
}

.success-icon {
  animation: iconScale 0.5s ease-out 0.4s both;
}

/* Animated Checkmark */
.success-checkmark {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: block;
  stroke-width: 3;
  stroke: #10b981;
  stroke-miterlimit: 10;
  box-shadow: inset 0px 0px 0px #10b981;
  animation: checkmarkFill 0.4s ease-in-out 0.6s forwards, checkmarkScale 0.3s ease-in-out 0.9s both;
  position: relative;
}

.success-checkmark::after {
  content: '';
  width: 16px;
  height: 8px;
  position: absolute;
  left: 22px;
  top: 24px;
  border: 3px solid #10b981;
  border-top: none;
  border-right: none;
  transform: rotate(-45deg);
  opacity: 0;
  animation: checkmarkCheck 0.3s ease-in-out 1s forwards;
}

/* Submitted Data Display Mobile Styles */
.submitted-data-display {
  /* Ensure proper mobile spacing */
  margin-left: 0;
  margin-right: 0;
}

@media (max-width: 640px) {
  .submitted-data-display {
    border-radius: 1rem;
    margin-top: 1rem;
  }

  /* Ensure mobile modal takes full screen with no scroll */
  .success-modal {
    padding: 0;
    margin: 0;
    overflow: hidden;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }

  .success-modal-container {
    padding: 0;
    margin: 0;
    align-items: stretch;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .success-modal-mobile {
    /* Force full screen on mobile with no scroll */
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100vw;
    height: 100vh;
    height: 100dvh;
    max-width: 100vw;
    max-height: 100vh;
    max-height: 100dvh;
    border-radius: 0;
    margin: 0;
    padding: 0;
    overflow: hidden;
    /* Prevent any content from causing scroll */
    box-sizing: border-box;
  }

  /* Ensure content fits within viewport */
  .success-modal-mobile > div {
    height: 100%;
    max-height: 100vh;
    max-height: 100dvh;
    overflow: hidden;
  }
}

/* Keyframe Animations */
@keyframes modalFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes modalSlideUp {
  from {
    opacity: 0;
    transform: translateY(50px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes modalSlideUpMobile {
  from {
    opacity: 0;
    transform: translateY(100vh);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes iconPulse {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes iconScale {
  from {
    transform: scale(0);
  }
  to {
    transform: scale(1);
  }
}

@keyframes checkmarkFill {
  100% {
    box-shadow: inset 0px 0px 0px 30px #10b981;
  }
}

@keyframes checkmarkScale {
  0%, 100% {
    transform: none;
  }
  50% {
    transform: scale3d(1.1, 1.1, 1);
  }
}

@keyframes checkmarkCheck {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

/* Enhanced button hover effects in modal */
.success-modal .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.success-modal .btn:active {
  transform: translateY(0);
}

/* Glass effect for modal content */
[data-theme="hlenergy-dark"] .success-modal-content {
  background: rgba(10, 31, 27, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

[data-theme="hlenergy-dark"] .success-modal-mobile {
  background: rgba(10, 31, 27, 0.98);
}

/* Prevent body scroll when modal is open */
body.modal-open {
  overflow: hidden !important;
  height: 100vh;
  height: 100dvh;
  position: fixed;
  width: 100%;
  top: 0;
  left: 0;
}

/* Prevent scroll on html element too */
html.modal-open {
  overflow: hidden !important;
  height: 100vh;
  height: 100dvh;
}

/* Additional mobile scroll prevention */
@media (max-width: 640px) {
  body.modal-open {
    position: fixed !important;
    width: 100vw !important;
    height: 100vh !important;
    height: 100dvh !important;
    overflow: hidden !important;
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: none;
  }

  html.modal-open {
    overflow: hidden !important;
    height: 100vh !important;
    height: 100dvh !important;
    overscroll-behavior: none;
  }
}
.hover\:shadow-\[\#12816c\]\/10:hover {
  box-shadow: 0 20px 25px -5px rgba(18, 129, 108, 0.1), 0 10px 10px -5px rgba(18, 129, 108, 0.04);
}

.hover\:shadow-\[\#5cad64\]\/10:hover {
  box-shadow: 0 20px 25px -5px rgba(92, 173, 100, 0.1), 0 10px 10px -5px rgba(92, 173, 100, 0.04);
}

.hover\:shadow-\[\#389868\]\/10:hover {
  box-shadow: 0 20px 25px -5px rgba(56, 152, 104, 0.1), 0 10px 10px -5px rgba(56, 152, 104, 0.04);
}

.hover\:shadow-\[\#eaaa34\]\/10:hover {
  box-shadow: 0 20px 25px -5px rgba(234, 170, 52, 0.1), 0 10px 10px -5px rgba(234, 170, 52, 0.04);
}

/* Icon container shadow effects */
.shadow-\[\#12816c\]\/10 {
  box-shadow: 0 4px 6px -1px rgba(18, 129, 108, 0.1), 0 2px 4px -1px rgba(18, 129, 108, 0.06);
}

.shadow-\[\#5cad64\]\/10 {
  box-shadow: 0 4px 6px -1px rgba(92, 173, 100, 0.1), 0 2px 4px -1px rgba(92, 173, 100, 0.06);
}

.shadow-\[\#389868\]\/10 {
  box-shadow: 0 4px 6px -1px rgba(56, 152, 104, 0.1), 0 2px 4px -1px rgba(56, 152, 104, 0.06);
}

.shadow-\[\#eaaa34\]\/10 {
  box-shadow: 0 4px 6px -1px rgba(234, 170, 52, 0.1), 0 2px 4px -1px rgba(234, 170, 52, 0.06);
}

/* Smooth rotation animation */
.group-hover\:rotate-3 {
  transition: transform 0.3s ease-in-out;
}

.group:hover .group-hover\:rotate-3 {
  transform: rotate(3deg) scale(1.1);
}
</style>
