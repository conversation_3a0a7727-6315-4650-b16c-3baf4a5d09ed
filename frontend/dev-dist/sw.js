if(!self.define){let e,s={};const n=(n,a)=>(n=new URL(n+".js",a).href,s[n]||new Promise(s=>{if("document"in self){const e=document.createElement("script");e.src=n,e.onload=s,document.head.appendChild(e)}else e=n,importScripts(n),s()}).then(()=>{let e=s[n];if(!e)throw new Error(`Module ${n} didn’t register its module`);return e}));self.define=(a,t)=>{const i=e||("document"in self?document.currentScript.src:"")||location.href;if(s[i])return;let c={};const o=e=>n(e,i),m={module:{uri:i},exports:c,require:o};s[i]=Promise.all(a.map(e=>m[e]||o(e))).then(e=>(t(...e),c))}}define(["./workbox-c32bfdef"],function(e){"use strict";self.skipWaiting(),e.clientsClaim(),e.precacheAndRoute([{url:"registerSW.js",revision:"3ca0b8505b4bec776b69afdba2768812"},{url:"/index.html",revision:"0.a857odaoa4"}],{}),e.cleanupOutdatedCaches(),e.registerRoute(new e.NavigationRoute(e.createHandlerBoundToURL("/index.html"),{allowlist:[/^\/$/],denylist:[/^\/_/,/^\/api\//,/^\/sw\.js$/,/^\/workbox-/,/^\/firebase-messaging-sw\.js$/,/\.(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot)$/i,/\/manifest\.json$/,/\/robots\.txt$/,/\/sitemap\.xml$/]})),e.registerRoute(({request:e})=>"navigate"===e.mode,new e.NetworkFirst({cacheName:"navigation-cache",networkTimeoutSeconds:3,plugins:[new e.ExpirationPlugin({maxEntries:50,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/^https:\/\/fonts\.googleapis\.com\/.*/i,new e.CacheFirst({cacheName:"google-fonts-stylesheets",plugins:[new e.ExpirationPlugin({maxEntries:10,maxAgeSeconds:31536e3})]}),"GET"),e.registerRoute(/^https:\/\/fonts\.gstatic\.com\/.*/i,new e.CacheFirst({cacheName:"google-fonts-webfonts",plugins:[new e.ExpirationPlugin({maxEntries:30,maxAgeSeconds:31536e3})]}),"GET"),e.registerRoute(/^http:\/\/localhost:3001\/api\/.*/i,new e.NetworkFirst({cacheName:"api-cache",networkTimeoutSeconds:3,plugins:[new e.ExpirationPlugin({maxEntries:100,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:png|jpg|jpeg|svg|gif|webp)$/i,new e.CacheFirst({cacheName:"images-cache",plugins:[new e.ExpirationPlugin({maxEntries:100,maxAgeSeconds:2592e3})]}),"GET"),e.registerRoute(/\.(?:css|js)$/i,new e.StaleWhileRevalidate({cacheName:"static-resources",plugins:[new e.ExpirationPlugin({maxEntries:100,maxAgeSeconds:604800})]}),"GET"),e.registerRoute(/\.(?:html)$/i,new e.NetworkFirst({cacheName:"pages-cache",networkTimeoutSeconds:3,plugins:[new e.ExpirationPlugin({maxEntries:50,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/^http:\/\/localhost:3001\/api\/v1\/auth\/.*/i,new e.NetworkOnly({cacheName:"auth-cache",plugins:[]}),"GET"),e.registerRoute(/^http:\/\/localhost:3001\/api\/v1\/(dashboard|analytics|stats)\/.*/i,new e.StaleWhileRevalidate({cacheName:"dashboard-cache",plugins:[new e.ExpirationPlugin({maxEntries:50,maxAgeSeconds:3600})]}),"GET"),e.registerRoute(/^https:\/\/cdn\..*/i,new e.CacheFirst({cacheName:"cdn-cache",plugins:[new e.ExpirationPlugin({maxEntries:100,maxAgeSeconds:31536e3})]}),"GET"),e.registerRoute(/\.(?:mp4|webm|ogg|mp3|wav|flac|aac)$/i,new e.CacheFirst({cacheName:"media-cache",plugins:[new e.ExpirationPlugin({maxEntries:20,maxAgeSeconds:2592e3}),new e.RangeRequestsPlugin]}),"GET"),self.__WB_DISABLE_DEV_LOGS=!0});
